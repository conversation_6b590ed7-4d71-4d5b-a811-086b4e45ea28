globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1030":{"*":{"id":"9860","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"1851":{"*":{"id":"4130","name":"*","chunks":[],"async":false}},"2300":{"*":{"id":"932","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"5720":{"*":{"id":"3492","name":"*","chunks":[],"async":false}},"6492":{"*":{"id":"1048","name":"*","chunks":[],"async":false}},"6821":{"*":{"id":"773","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"6996":{"*":{"id":"5235","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"8175":{"*":{"id":"5587","name":"*","chunks":[],"async":false}},"8393":{"*":{"id":"5227","name":"*","chunks":[],"async":false}},"8781":{"*":{"id":"6351","name":"*","chunks":[],"async":false}},"9429":{"*":{"id":"8756","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":8393,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":8393,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":8175,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":8175,"name":"*","chunks":[],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":2093,"name":"*","chunks":["177","static/chunks/app/layout-e90bfe64106b829e.js"],"async":false},"C:\\git_projects\\AutoFormAI\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7735,"name":"*","chunks":["177","static/chunks/app/layout-e90bfe64106b829e.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-e90bfe64106b829e.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\Demo.tsx":{"id":1030,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\FAQ.tsx":{"id":1851,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\Features.tsx":{"id":9429,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\Footer.tsx":{"id":6821,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\Header.tsx":{"id":6996,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\Hero.tsx":{"id":5720,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\HowItWorks.tsx":{"id":8781,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\TechStack.tsx":{"id":6492,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false},"C:\\git_projects\\AutoFormAI\\src\\components\\UseCases.tsx":{"id":2300,"name":"*","chunks":["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-0545e7a855e6d1eb.js"],"async":false}},"entryCSSFiles":{"C:\\git_projects\\AutoFormAI\\src\\":[],"C:\\git_projects\\AutoFormAI\\src\\app\\layout":[{"inlined":false,"path":"static/css/8da84c4788320fca.css"}],"C:\\git_projects\\AutoFormAI\\src\\app\\page":[],"C:\\git_projects\\AutoFormAI\\src\\app\\_not-found\\page":[]},"rscModuleMapping":{"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1030":{"*":{"id":"5659","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"1851":{"*":{"id":"2684","name":"*","chunks":[],"async":false}},"2300":{"*":{"id":"1866","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"5720":{"*":{"id":"2554","name":"*","chunks":[],"async":false}},"6492":{"*":{"id":"6976","name":"*","chunks":[],"async":false}},"6821":{"*":{"id":"8659","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"6996":{"*":{"id":"4597","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"8175":{"*":{"id":"4817","name":"*","chunks":[],"async":false}},"8393":{"*":{"id":"6133","name":"*","chunks":[],"async":false}},"8781":{"*":{"id":"4447","name":"*","chunks":[],"async":false}},"9429":{"*":{"id":"1095","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}
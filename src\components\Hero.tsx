"use client";

import { motion } from "framer-motion";
// No icons needed for simplified design
import { useState, useEffect } from "react";

const Hero = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="loading-skeleton w-32 h-8 mx-auto mb-8 rounded-full"></div>
          <div className="loading-skeleton w-full max-w-4xl h-16 mx-auto mb-6 rounded-lg"></div>
          <div className="loading-skeleton w-full max-w-3xl h-12 mx-auto mb-6 rounded-lg"></div>
          <div className="loading-skeleton w-full max-w-2xl h-6 mx-auto mb-12 rounded"></div>
          <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="loading-skeleton w-40 h-12 rounded-full"></div>
            <div className="loading-skeleton w-40 h-12 rounded-full"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-yellow-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Main Heading */}
        <motion.h1
          className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 px-2"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          AutoForm AI
        </motion.h1>

        {/* Subheading */}
        <motion.h2
          className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold text-gray-700 mb-6 max-w-4xl mx-auto px-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Stop copy-pasting your life story into every form.{" "}
          <span className="text-black">Let AI do the boring stuff.</span>
        </motion.h2>

        {/* Description */}
        <motion.p
          className="text-base md:text-lg text-gray-600 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          Write your info once in Markdown files. Tag them smart. Watch AI fill
          every job application, contact form, and survey while you grab coffee.{" "}
          <span className="font-semibold text-gray-800">
            Save 2+ hours daily.
          </span>
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 px-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <motion.button
            className="w-full sm:w-auto flex items-center justify-center space-x-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-2xl text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>Add to Chrome</span>
          </motion.button>

          <motion.button
            className="w-full sm:w-auto flex items-center justify-center space-x-2 bg-white text-gray-900 px-6 sm:px-8 py-3 sm:py-4 rounded-2xl text-base sm:text-lg font-semibold border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-300 shadow-lg"
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>Watch Demo</span>
          </motion.button>
        </motion.div>

        {/* Feature Highlights */}
        <motion.div
          className="grid grid-cols-2 sm:flex sm:flex-wrap items-center justify-center gap-3 sm:gap-6 mt-8 sm:mt-12 text-xs sm:text-sm font-medium px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <div className="flex items-center justify-center space-x-2 bg-green-50 px-3 sm:px-4 py-2 rounded-full">
            <span className="text-green-700">Local Processing</span>
          </div>
          <div className="flex items-center justify-center space-x-2 bg-yellow-50 px-3 sm:px-4 py-2 rounded-full">
            <span className="text-yellow-700">3 Second Fill</span>
          </div>
          <div className="flex items-center justify-center space-x-2 bg-blue-50 px-3 sm:px-4 py-2 rounded-full">
            <span className="text-blue-700">Any Website</span>
          </div>
          <div className="flex items-center justify-center space-x-2 bg-purple-50 px-3 sm:px-4 py-2 rounded-full">
            <span className="text-purple-700">Open Source</span>
          </div>
        </motion.div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.5 }}
        >
          <motion.div
            className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <motion.div
              className="w-1 h-3 bg-gray-400 rounded-full mt-2"
              animate={{ y: [0, 6, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;

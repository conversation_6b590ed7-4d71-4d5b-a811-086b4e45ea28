"use client";

import { motion } from "framer-motion";
import {
  <PERSON>R<PERSON>,
  <PERSON>,
  <PERSON>rk<PERSON>,
  Zap,
  Shield,
  Globe,
  Download,
  <PERSON>,
  Brain,
} from "lucide-react";
import { useState, useEffect } from "react";

const Hero = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="loading-skeleton w-32 h-8 mx-auto mb-8 rounded-full"></div>
          <div className="loading-skeleton w-full max-w-4xl h-16 mx-auto mb-6 rounded-lg"></div>
          <div className="loading-skeleton w-full max-w-3xl h-12 mx-auto mb-6 rounded-lg"></div>
          <div className="loading-skeleton w-full max-w-2xl h-6 mx-auto mb-12 rounded"></div>
          <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="loading-skeleton w-40 h-12 rounded-full"></div>
            <div className="loading-skeleton w-40 h-12 rounded-full"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-yellow-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Badge */}
        <motion.div
          className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Sparkles className="w-4 h-4" />
          <span>Free Forever • Privacy First • Zero BS</span>
        </motion.div>

        {/* Main Heading */}
        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl mb-4 shadow-2xl"
            whileHover={{ scale: 1.05, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <Brain className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 text-white" />
          </motion.div>
          <br />
          AutoForm AI
        </motion.h1>

        {/* Subheading */}
        <motion.h2
          className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-semibold text-gray-700 mb-6 max-w-4xl mx-auto px-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Stop copy-pasting your life story into every form.{" "}
          <span className="text-black">Let AI do the boring stuff.</span>
        </motion.h2>

        {/* Description */}
        <motion.p
          className="text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          Write your info once in Markdown files. Tag them smart. Watch AI fill
          every job application, contact form, and survey while you grab coffee.{" "}
          <span className="font-semibold text-gray-800">
            Save 2+ hours daily.
          </span>
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <motion.button
            className="group relative flex items-center space-x-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-2xl text-lg font-semibold shadow-2xl overflow-hidden"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-700 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <Download className="w-5 h-5 relative z-10 group-hover:animate-bounce" />
            <span className="relative z-10">Add to Chrome</span>
            <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
          </motion.button>

          <motion.button
            className="group flex items-center space-x-3 bg-white/80 backdrop-blur-sm text-gray-900 px-8 py-4 rounded-2xl text-lg font-semibold border-2 border-gray-200/50 hover:border-gray-300 hover:bg-white transition-all duration-300 shadow-xl"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="relative">
              <Play className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-purple-500 rounded-full opacity-0 group-hover:opacity-20 animate-ping" />
            </div>
            <span>Try the Demo</span>
          </motion.button>
        </motion.div>

        {/* Feature Highlights */}
        <motion.div
          className="flex flex-wrap items-center justify-center gap-8 mt-12 text-sm font-medium"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <div className="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full">
            <Shield className="w-4 h-4 text-green-600" />
            <span className="text-green-700">100% Local Processing</span>
          </div>
          <div className="flex items-center space-x-2 bg-yellow-50 px-4 py-2 rounded-full">
            <Zap className="w-4 h-4 text-yellow-600" />
            <span className="text-yellow-700">Fills in 3 Seconds</span>
          </div>
          <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-full">
            <Globe className="w-4 h-4 text-blue-600" />
            <span className="text-blue-700">Any Website</span>
          </div>
          <div className="flex items-center space-x-2 bg-purple-50 px-4 py-2 rounded-full">
            <Star className="w-4 h-4 text-purple-600" />
            <span className="text-purple-700">Free Forever</span>
          </div>
        </motion.div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.5 }}
        >
          <motion.div
            className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <motion.div
              className="w-1 h-3 bg-gray-400 rounded-full mt-2"
              animate={{ y: [0, 6, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;

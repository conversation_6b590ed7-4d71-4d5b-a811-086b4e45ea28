(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1030:(e,t,s)=>{"use strict";s.d(t,{default:()=>A});var a=s(5155),i=s(2115),r=s(2605),l=s(760),n=s(9869),o=s(3332),c=s(3311),d=s(5196),x=s(5690),m=s(5169),h=s(2138),p=s(1700),u=s(4416),y=s(9434);let g=p.bL,b=p.l9,f=p.ZL;p.bm;let j=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(p.hJ,{ref:t,className:(0,y.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...i})});j.displayName=p.hJ.displayName;let w=i.forwardRef((e,t)=>{let{className:s,children:i,...r}=e;return(0,a.jsxs)(f,{children:[(0,a.jsx)(j,{}),(0,a.jsxs)(p.UC,{ref:t,className:(0,y.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...r,children:[i,(0,a.jsxs)(p.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 data-[state=open]:text-gray-500",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});w.displayName=p.UC.displayName;let v=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,y.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};v.displayName="DialogHeader";let N=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(p.hE,{ref:t,className:(0,y.cn)("text-lg font-semibold leading-none tracking-tight",s),...i})});N.displayName=p.hE.displayName;let k=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(p.VY,{ref:t,className:(0,y.cn)("text-sm text-gray-500",s),...i})});k.displayName=p.VY.displayName;let A=()=>{let[e,t]=(0,i.useState)(!1),[s,p]=(0,i.useState)(0),u=[{title:"Upload Your Markdown",description:"Let's start with your about-me.md file",content:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors",children:[(0,a.jsx)(n.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Drop your about-me.md file here"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Or click to browse"})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 font-mono",children:"\uD83D\uDCC4 about-me.md loaded successfully!"})})]})},{title:"Add Tags",description:"Tag your content so AI knows what's up",content:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:["#resume","#startup","#founder-story"].map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-black text-white",children:[(0,a.jsx)(o.A,{className:"w-3 h-3 mr-1"}),e]},e))}),(0,a.jsx)("input",{type:"text",placeholder:"Add more tags...",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"})]})},{title:"AI Magic Happens",description:"Watch as AI analyzes the form and suggests answers",content:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,a.jsx)(c.A,{className:"w-6 h-6 text-purple-600 animate-pulse"}),(0,a.jsx)("span",{className:"text-lg font-semibold",children:"AI is thinking..."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-2 bg-purple-200 rounded-full overflow-hidden",children:(0,a.jsx)(r.P.div,{className:"h-full bg-purple-600 rounded-full",initial:{width:0},animate:{width:"100%"},transition:{duration:2,ease:"easeInOut"}})}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Analyzing form fields... Matching with your content... Generating responses..."})]})]})})},{title:"Review & Edit",description:"Check the AI suggestions and make any tweaks",content:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,a.jsx)("input",{type:"text",value:"John Doe",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent",readOnly:!0})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Why do you want this job?"}),(0,a.jsx)("textarea",{value:"I'm passionate about building products that solve real problems. My experience in full-stack development and my love for clean code make me a perfect fit for this role.",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent h-20",readOnly:!0})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-600",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"All fields filled automatically!"})]})]})},{title:"Submit with Confidence",description:"Your form is ready to go!",content:(0,a.jsxs)("div",{className:"text-center space-y-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto",children:(0,a.jsx)(d.A,{className:"w-10 h-10 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Form Completed! \uD83C\uDF89"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Your application has been filled out perfectly. Time saved: 15 minutes. Stress level: Minimal. Chance of typos: Zero."})]}),(0,a.jsx)("button",{className:"w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors",children:"Submit Application"})]})}];return(0,a.jsx)("section",{id:"demo",className:"py-20 bg-gradient-to-br from-blue-50 to-purple-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-4",children:"See It In Action"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto mb-8",children:"Don't just take our word for it. Watch AutoForm AI work its magic on a real job application form. No smoke, no mirrors, just pure automation."}),(0,a.jsxs)(g,{open:e,onOpenChange:t,children:[(0,a.jsx)(b,{asChild:!0,children:(0,a.jsxs)(r.P.button,{className:"inline-flex items-center space-x-3 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:[(0,a.jsx)(x.A,{className:"w-6 h-6"}),(0,a.jsx)("span",{children:"\uD83E\uDDE0 Try Smart Fill Demo"})]})}),(0,a.jsxs)(w,{className:"max-w-2xl",children:[(0,a.jsxs)(v,{children:[(0,a.jsx)(N,{className:"text-2xl font-bold",children:u[s].title}),(0,a.jsx)(k,{className:"text-lg",children:u[s].description})]}),(0,a.jsx)("div",{className:"py-6",children:(0,a.jsx)(l.N,{mode:"wait",children:(0,a.jsx)(r.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:u[s].content},s)})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("button",{onClick:()=>{s>0&&p(s-1)},disabled:0===s,className:"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:u.map((e,t)=>(0,a.jsx)("div",{className:"w-2 h-2 rounded-full transition-colors ".concat(t===s?"bg-black":"bg-gray-300")},t))}),s<u.length-1?(0,a.jsxs)("button",{onClick:()=>{s<u.length-1&&p(s+1)},className:"flex items-center space-x-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors",children:[(0,a.jsx)("span",{children:"Next"}),(0,a.jsx)(h.A,{className:"w-4 h-4"})]}):(0,a.jsxs)("button",{onClick:()=>{p(0),t(!1)},className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)("span",{children:"Try Again"}),(0,a.jsx)(c.A,{className:"w-4 h-4"})]})]})]})]})]})})})}},1851:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var a=s(5155),i=s(2115),r=s(2605),l=s(760),n=s(5525),o=s(9803),c=s(4869),d=s(446),x=s(6474);let m=()=>{let[e,t]=(0,i.useState)(0),s=[{icon:n.A,emoji:"\uD83D\uDD10",question:"Is my data safe?",answer:"Yes. Local-first. Your ex doesn't have access. Neither do we. All your data stays on your machine, encrypted and secure. We don't have servers to hack because we don't have servers."},{icon:o.A,emoji:"\uD83D\uDD11",question:"What's needed?",answer:"Just your Markdown files + OpenAI/Gemini API key. That's it. No credit card, no subscription, no selling your soul to the form-filling gods."},{icon:c.A,emoji:"\uD83C\uDF10",question:"Where does it work?",answer:"Job boards, startup grants, hackathons, taxes. Almost everywhere. If it's a form on the internet, we probably support it. If we don't, we'll add support faster than you can say 'manual data entry'."},{icon:d.A,emoji:"\uD83D\uDC1E",question:"Will it break some forms?",answer:"Yes. That's life. But it'll try its best. We're constantly improving our form detection and filling algorithms. When it doesn't work, it fails gracefully and lets you know."}];return(0,a.jsx)("section",{className:"py-20 bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-4",children:"Questions? We've Got Answers."}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"The most common questions about AutoForm AI, answered with our signature blend of honesty and humor."})]}),(0,a.jsx)("div",{className:"space-y-4",children:s.map((s,i)=>(0,a.jsxs)(r.P.div,{className:"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*i},viewport:{once:!0},children:[(0,a.jsxs)("button",{onClick:()=>(s=>{t(e===s?null:s)})(i),className:"w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-black rounded-xl flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xl",children:s.emoji})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 flex items-center space-x-2",children:(0,a.jsx)("span",{children:s.question})}),(0,a.jsx)(s.icon,{className:"w-5 h-5 text-gray-500 mt-1"})]})]}),(0,a.jsx)(r.P.div,{animate:{rotate:180*(e===i)},transition:{duration:.3},children:(0,a.jsx)(x.A,{className:"w-6 h-6 text-gray-500"})})]}),(0,a.jsx)(l.N,{children:e===i&&(0,a.jsx)(r.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:(0,a.jsx)("div",{className:"px-8 pb-6",children:(0,a.jsx)("div",{className:"pl-16",children:(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:s.answer})})})})})]},i))}),(0,a.jsx)(r.P.div,{className:"text-center mt-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-lg border border-gray-200",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Still Have Questions?"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"We're here to help! Reach out to us and we'll get back to you faster than you can fill out a contact form manually."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,a.jsxs)(r.P.button,{className:"inline-flex items-center space-x-2 bg-black text-white px-6 py-3 rounded-full font-semibold hover:bg-gray-800 transition-colors duration-200",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)("span",{children:"Contact Support"}),(0,a.jsx)("span",{children:"\uD83D\uDCAC"})]}),(0,a.jsxs)(r.P.button,{className:"inline-flex items-center space-x-2 bg-gray-100 text-gray-900 px-6 py-3 rounded-full font-semibold hover:bg-gray-200 transition-colors duration-200",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)("span",{children:"Read Docs"}),(0,a.jsx)("span",{children:"\uD83D\uDCDA"})]})]})]})})]})})}},2300:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(5155),i=s(2605),r=s(7576),l=s(8136),n=s(9964),o=s(9074);let c=()=>{let e=[{icon:r.A,title:"Job Applications",description:"resume.md + about-me.md = LinkedIn Autofill on steroids",emoji:"\uD83D\uDCBC",examples:["Cover letters","Application forms","LinkedIn profiles","Portfolio submissions"]},{icon:l.A,title:"Government Forms",description:"id.md + address.md = Less pain at 3PM on a Friday",emoji:"\uD83C\uDFDB️",examples:["Tax forms","Visa applications","License renewals","Benefits claims"]},{icon:n.A,title:"Startup Pitches",description:"pitch.md + founder.md = One-click YC dreams",emoji:"\uD83D\uDE80",examples:["Accelerator applications","Grant proposals","Investor forms","Competition entries"]},{icon:o.A,title:"Event Registration",description:"socials.md = RSVP like the legend you are",emoji:"\uD83C\uDF9F️",examples:["Conference registration","Workshop signups","Meetup RSVPs","Webinar forms"]}],t={hidden:{opacity:0,y:30},visible:{opacity:1,y:0}};return(0,a.jsx)("section",{className:"py-20 bg-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(i.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-4",children:"Use Cases That Matter"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"From job hunting to government paperwork, AutoForm AI has your back. Because life's too short to copy-paste the same info 47 times."})]}),(0,a.jsx)(i.P.div,{className:"grid grid-cols-1 md:grid-cols-2 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:e.map((e,s)=>(0,a.jsxs)(i.P.div,{className:"group relative bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200",variants:t,whileHover:{y:-5},children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-black rounded-2xl flex items-center justify-center group-hover:bg-gray-800 transition-colors duration-300",children:(0,a.jsx)("span",{className:"text-2xl",children:e.emoji})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 group-hover:text-black transition-colors duration-300",children:e.title}),(0,a.jsx)(e.icon,{className:"w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300 mt-1"})]})]}),(0,a.jsx)("p",{className:"text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300 mb-6",children:e.description}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wide",children:"Perfect for:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.examples.map((e,t)=>(0,a.jsx)("span",{className:"inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300",children:e},t))})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]},s))}),(0,a.jsx)(i.P.div,{className:"text-center mt-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-900 to-black rounded-2xl p-8 text-white",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Automate Your Life?"}),(0,a.jsx)("p",{className:"text-lg text-gray-300 mb-6 max-w-2xl mx-auto",children:"Join thousands of smart people who've already said goodbye to manual form filling. Your future self will thank you."}),(0,a.jsxs)(i.P.button,{className:"inline-flex items-center space-x-2 bg-white text-black px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:[(0,a.jsx)("span",{children:"Get AutoForm AI"}),(0,a.jsx)("span",{children:"\uD83D\uDE80"})]})]})})]})})}},4615:(e,t,s)=>{Promise.resolve().then(s.bind(s,1030)),Promise.resolve().then(s.bind(s,1851)),Promise.resolve().then(s.bind(s,9429)),Promise.resolve().then(s.bind(s,6821)),Promise.resolve().then(s.bind(s,6996)),Promise.resolve().then(s.bind(s,5720)),Promise.resolve().then(s.bind(s,8781)),Promise.resolve().then(s.bind(s,6492)),Promise.resolve().then(s.bind(s,2300))},5720:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(5155),i=s(2605),r=s(3311),l=s(2138),n=s(5690),o=s(2115);let c=()=>{let[e,t]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{t(!0)},[]),e)?(0,a.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"}),(0,a.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"}),(0,a.jsx)("div",{className:"absolute top-40 left-40 w-80 h-80 bg-yellow-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"})]}),(0,a.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsxs)(i.P.div,{className:"inline-flex items-center space-x-2 bg-black text-white px-4 py-2 rounded-full text-sm font-medium mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},children:[(0,a.jsx)(r.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Open source • Local-first • Actually useful"})]}),(0,a.jsxs)(i.P.h1,{className:"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:[(0,a.jsx)("span",{className:"text-5xl sm:text-6xl md:text-7xl lg:text-8xl mr-2 sm:mr-4",children:"\uD83E\uDDE0"}),(0,a.jsx)("br",{}),"AutoForm AI"]}),(0,a.jsxs)(i.P.h2,{className:"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-semibold text-gray-700 mb-6 max-w-4xl mx-auto px-4",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:["Let your words live in Markdown."," ",(0,a.jsx)("span",{className:"text-black",children:"Let your forms fill themselves."})]}),(0,a.jsx)(i.P.p,{className:"text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},children:"Your lazy best friend who fills job applications, government forms, startup pitches—automagically."}),(0,a.jsxs)(i.P.div,{className:"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:[(0,a.jsxs)(i.P.button,{className:"flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:[(0,a.jsx)(r.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Add to Chrome"}),(0,a.jsx)(l.A,{className:"w-5 h-5"})]}),(0,a.jsxs)(i.P.button,{className:"flex items-center space-x-2 bg-white text-gray-900 px-8 py-4 rounded-full text-lg font-semibold border-2 border-gray-200 hover:border-gray-300 transition-colors duration-200 shadow-lg",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:[(0,a.jsx)(n.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Try the Demo"})]})]}),(0,a.jsx)(i.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1.5},children:(0,a.jsx)(i.P.div,{className:"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,a.jsx)(i.P.div,{className:"w-1 h-3 bg-gray-400 rounded-full mt-2",animate:{y:[0,6,0]},transition:{duration:2,repeat:1/0}})})})]})]}):(0,a.jsx)("section",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden",children:(0,a.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("div",{className:"loading-skeleton w-32 h-8 mx-auto mb-8 rounded-full"}),(0,a.jsx)("div",{className:"loading-skeleton w-full max-w-4xl h-16 mx-auto mb-6 rounded-lg"}),(0,a.jsx)("div",{className:"loading-skeleton w-full max-w-3xl h-12 mx-auto mb-6 rounded-lg"}),(0,a.jsx)("div",{className:"loading-skeleton w-full max-w-2xl h-6 mx-auto mb-12 rounded"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,a.jsx)("div",{className:"loading-skeleton w-40 h-12 rounded-full"}),(0,a.jsx)("div",{className:"loading-skeleton w-40 h-12 rounded-full"})]})]})})}},6492:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var a=s(5155),i=s(2605),r=s(3127),l=s(9621),n=s(1539),o=s(5525),c=s(4869);let d=()=>{let e=[{category:"UI & Design",icon:r.A,emoji:"\uD83C\uDFA8",technologies:[{name:"React",description:"For building the UI"},{name:"Tailwind CSS",description:"For styling that doesn't suck"},{name:"Framer Motion",description:"For animations that wow"}]},{category:"Core Engine",icon:l.A,emoji:"⚡",technologies:[{name:"Chrome Extension (MV3)",description:"The magic happens here"},{name:"Markdown Parser",description:"Your .md files, understood"},{name:"DOM Crawler",description:"Finds forms like a bloodhound"}]},{category:"AI & Processing",icon:n.A,emoji:"\uD83E\uDDE0",technologies:[{name:"Gemini API",description:"Google's finest AI"},{name:"ChatGPT API",description:"OpenAI's brain power"},{name:"Context Engine",description:"Understands what you mean"}]},{category:"Storage & Privacy",icon:o.A,emoji:"\uD83D\uDD10",technologies:[{name:"Chrome localStorage",description:"Your data stays local"},{name:"File System Access API",description:"Direct file access"},{name:"Zero Cloud Dependency",description:"No servers, no worries"}]}],t={hidden:{opacity:0,y:30},visible:{opacity:1,y:0}};return(0,a.jsx)("section",{className:"py-20 bg-gradient-to-br from-gray-900 to-black text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(i.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Built with Modern Tech"}),(0,a.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"We use the latest and greatest technologies to make sure AutoForm AI is fast, reliable, and secure. No legacy code, no technical debt, just pure innovation."})]}),(0,a.jsx)(i.P.div,{className:"grid grid-cols-1 md:grid-cols-2 gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:e.map((e,s)=>(0,a.jsxs)(i.P.div,{className:"group relative bg-gray-800 rounded-2xl p-8 hover:bg-gray-700 transition-all duration-300 border border-gray-700 hover:border-gray-600",variants:t,whileHover:{y:-5},children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-white rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300",children:(0,a.jsx)("span",{className:"text-2xl",children:e.emoji})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300",children:e.category}),(0,a.jsx)(e.icon,{className:"w-6 h-6 text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-1"})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:e.technologies.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:e.description})]})]},t))}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]},s))}),(0,a.jsxs)(i.P.div,{className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-4xl font-bold text-green-400",children:"100%"}),(0,a.jsx)("div",{className:"text-gray-300",children:"Open Source"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-4xl font-bold text-blue-400",children:"0"}),(0,a.jsx)("div",{className:"text-gray-300",children:"Servers Required"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-4xl font-bold text-purple-400",children:"∞"}),(0,a.jsx)("div",{className:"text-gray-300",children:"Forms Supported"})]})]}),(0,a.jsx)(i.P.div,{className:"text-center mt-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Open Source & Proud"}),(0,a.jsx)("p",{className:"text-lg text-gray-300 mb-6 max-w-2xl mx-auto",children:"Every line of code is open for inspection. Contribute, fork, or just admire our beautiful architecture. We believe in transparency."}),(0,a.jsxs)(i.P.button,{className:"inline-flex items-center space-x-2 bg-white text-black px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:[(0,a.jsx)(c.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"View on GitHub"})]})]})})]})})}},6821:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(5155),i=s(2605),r=s(9099),l=s(7434),n=s(8126),o=s(1976);let c=()=>{let e=[{name:"GitHub",href:"#",icon:r.A},{name:"Docs",href:"#",icon:l.A},{name:"Add to Chrome",href:"#",icon:n.A}];return(0,a.jsx)("footer",{className:"bg-black text-white py-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsxs)(i.P.div,{className:"flex items-center space-x-2 mb-4",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("span",{className:"text-3xl",children:"\uD83E\uDDE0"}),(0,a.jsx)("span",{className:"text-2xl font-bold",children:"AutoForm AI"})]}),(0,a.jsx)(i.P.p,{className:"text-gray-400 text-lg leading-relaxed",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:"The Chrome extension that ends the tyranny of online forms forever. Because life's too short to copy-paste the same info 47 times."})]}),(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)(i.P.h3,{className:"text-xl font-bold mb-6",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:"Quick Links"}),(0,a.jsx)(i.P.div,{className:"space-y-4",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:e.map((e,t)=>(0,a.jsxs)(i.P.a,{href:e.href,className:"flex items-center space-x-3 text-gray-400 hover:text-white transition-colors duration-200 group",whileHover:{x:5},children:[(0,a.jsx)(e.icon,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-200"}),(0,a.jsx)("span",{children:e.name})]},e.name))})]}),(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)(i.P.h3,{className:"text-xl font-bold mb-6",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:"Ready to Get Started?"}),(0,a.jsxs)(i.P.div,{className:"space-y-4",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},viewport:{once:!0},children:[(0,a.jsx)("p",{className:"text-gray-400",children:"Join the form-filling revolution. Your future self will thank you."}),(0,a.jsxs)(i.P.button,{className:"w-full bg-white text-black px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center space-x-2",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)(n.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Add to Chrome"}),(0,a.jsx)("span",{children:"\uD83D\uDD25"})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-800 my-8"}),(0,a.jsxs)(i.P.div,{className:"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},viewport:{once:!0},children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,a.jsx)("span",{children:"Made with"}),(0,a.jsx)(o.A,{className:"w-4 h-4 text-red-500"}),(0,a.jsx)("span",{children:"by Open Source Humans"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-6 text-gray-400",children:[(0,a.jsx)("span",{children:"MIT License"}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:"Built in India"}),(0,a.jsx)("span",{children:"\uD83C\uDDEE\uD83C\uDDF3"})]})]})]}),(0,a.jsx)(i.P.div,{className:"text-center mt-8 pt-8 border-t border-gray-800",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1,delay:.8},viewport:{once:!0},children:(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:'"The best form filler is the one you never have to use manually." - Ancient Developer Proverb'})})]})})}},6996:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var a=s(5155),i=s(2115),r=s(2605),l=s(9099),n=s(4416),o=s(4783),c=s(9434);let d=()=>{let[e,t]=(0,i.useState)(!1),[s,d]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{t(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let x=[{name:"Features",href:"#features"},{name:"How it works",href:"#how-it-works"},{name:"Demo",href:"#demo"},{name:"GitHub",href:"#github",icon:l.A}];return(0,a.jsx)(r.P.header,{className:(0,c.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",e?"bg-white/80 backdrop-blur-md border-b border-gray-200":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.6,ease:"easeOut"},children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)(r.P.div,{className:"flex items-center space-x-2",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83E\uDDE0"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AutoForm AI"})]}),(0,a.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:x.map(e=>(0,a.jsxs)(r.P.a,{href:e.href,className:"flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors duration-200",whileHover:{y:-2},whileTap:{y:0},children:[e.icon&&(0,a.jsx)(e.icon,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.name})]},e.name))}),(0,a.jsxs)(r.P.button,{className:"hidden md:flex items-center space-x-2 bg-black text-white px-6 py-2 rounded-full hover:bg-gray-800 transition-colors duration-200",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)("span",{children:"Get Extension"}),(0,a.jsx)("span",{children:"\uD83D\uDD25"})]}),(0,a.jsx)("button",{className:"md:hidden p-2",onClick:()=>d(!s),children:s?(0,a.jsx)(n.A,{className:"w-6 h-6"}):(0,a.jsx)(o.A,{className:"w-6 h-6"})})]}),s&&(0,a.jsx)(r.P.div,{className:"md:hidden py-4 border-t border-gray-200",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[x.map(e=>(0,a.jsxs)("a",{href:e.href,className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors duration-200",onClick:()=>d(!1),children:[e.icon&&(0,a.jsx)(e.icon,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.name})]},e.name)),(0,a.jsxs)("button",{className:"flex items-center justify-center space-x-2 bg-black text-white px-6 py-2 rounded-full hover:bg-gray-800 transition-colors duration-200 w-full",children:[(0,a.jsx)("span",{children:"Get Extension"}),(0,a.jsx)("span",{children:"\uD83D\uDD25"})]})]})})]})})}},8781:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(5155),i=s(2605),r=s(1788),l=s(9869),n=s(3332),o=s(2103),c=s(3717),d=s(2486);let x=()=>{let e=[{icon:r.A,title:"Install the Extension",description:"Add it to Chrome. Boom. You're 12% more productive already.",emoji:"⬇️"},{icon:l.A,title:"Drop Your Markdown Files",description:"about-me.md, job.md, awkward-college-phase.md — we don't judge.",emoji:"\uD83D\uDCC2"},{icon:n.A,title:"Tag What Matters",description:"Use #resume, #founder-story, #gov-form so AI knows what's up.",emoji:"\uD83C\uDFF7️"},{icon:o.A,title:"Click 'Fill Form'",description:"We crawl the DOM. LLMs whisper answers. You sip chai.",emoji:"\uD83D\uDDB1️"},{icon:c.A,title:"Review & Edit",description:"Didn't like that answer? Fix it. Or make it more spicy.",emoji:"✏️"},{icon:d.A,title:"Submit with Peace of Mind",description:"Your future self thanks you.",emoji:"\uD83C\uDFAF"}],t={hidden:{opacity:0,x:-30},visible:{opacity:1,x:0}};return(0,a.jsx)("section",{id:"how-it-works",className:"py-20 bg-gradient-to-br from-gray-50 to-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(i.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-4",children:"How It Works"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Six simple steps to form-filling enlightenment. It's easier than explaining why you need another Chrome extension."})]}),(0,a.jsx)(i.P.div,{className:"space-y-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:e.map((s,r)=>(0,a.jsxs)(i.P.div,{className:"flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group",variants:t,children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300",children:r+1}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-3xl",children:s.emoji}),(0,a.jsx)(s.icon,{className:"w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300",children:s.title}),(0,a.jsx)("p",{className:"text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300",children:s.description})]})]}),r<e.length-1&&(0,a.jsx)("div",{className:"hidden md:block absolute left-8 mt-20 w-0.5 h-16 bg-gray-200 group-hover:bg-gray-300 transition-colors duration-300"})]},r))}),(0,a.jsxs)(i.P.div,{className:"text-center mt-16 p-8 bg-white rounded-2xl shadow-lg border border-gray-100",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"That's it! \uD83C\uDF89"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"No PhD in computer science required. No sacrificing your firstborn to the form gods. Just pure, unadulterated form-filling magic."}),(0,a.jsxs)(i.P.button,{className:"inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:[(0,a.jsx)("span",{children:"Try It Now"}),(0,a.jsx)("span",{children:"✨"})]})]})]})})}},9429:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(5155),i=s(2605),r=s(7924),l=s(5657),n=s(7434),o=s(9376),c=s(2657),d=s(5525);let x=()=>{let e=[{icon:r.A,title:"Auto-Detects Any Form",description:"No matter the form. Even the weird ones your college admin made.",emoji:"\uD83D\uDD0D"},{icon:l.A,title:"AI That Actually Gets It",description:"Understands the question like a human. But doesn't complain.",emoji:"\uD83E\uDD16"},{icon:n.A,title:"Markdown = Brain",description:"Store your story in .md files. Tag them like a boss: #resume #pitch",emoji:"\uD83D\uDCC2"},{icon:o.A,title:"Context-Aware Engine",description:"The more you use it, the smarter it gets. Like a very nerdy Tamagotchi.",emoji:"\uD83E\uDDE0"},{icon:c.A,title:"Preview Before You Oops",description:"Don't worry, you can edit before submitting that 2AM cover letter.",emoji:"✍️"},{icon:d.A,title:"Local & API-Key Friendly",description:"Your data stays with you. Like a loyal dog. That knows JSON.",emoji:"\uD83D\uDD10"}],t={hidden:{opacity:0,y:30},visible:{opacity:1,y:0}};return(0,a.jsx)("section",{id:"features",className:"py-20 bg-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(i.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-4",children:"Features That Actually Work"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Because life's too short to fill out forms manually. Let AI do the boring stuff while you focus on the important things. Like choosing the perfect GIF."})]}),(0,a.jsx)(i.P.div,{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:e.map((e,s)=>(0,a.jsxs)(i.P.div,{className:"group relative bg-gray-50 rounded-2xl p-6 md:p-8 hover:bg-white hover:shadow-xl transition-all duration-300 border border-transparent hover:border-gray-200 h-full flex flex-col",variants:t,whileHover:{y:-5},children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,a.jsx)("span",{className:"text-3xl",children:e.emoji}),(0,a.jsx)(e.icon,{className:"w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 group-hover:text-gray-700 transition-colors duration-300",children:e.description})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]},s))}),(0,a.jsxs)(i.P.div,{className:"text-center mt-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},children:[(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"Ready to never fill out another form manually?"}),(0,a.jsxs)(i.P.button,{className:"inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:[(0,a.jsx)("span",{children:"Get Started Now"}),(0,a.jsx)("span",{children:"\uD83D\uDE80"})]})]})]})})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var a=s(2596),i=s(9688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,i.QP)((0,a.$)(t))}}},e=>{e.O(0,[409,441,964,358],()=>e(e.s=4615)),_N_E=e.O()}]);
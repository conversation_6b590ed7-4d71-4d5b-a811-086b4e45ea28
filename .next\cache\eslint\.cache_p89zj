[{"C:\\git_projects\\AutoFormAI\\src\\app\\layout.tsx": "1", "C:\\git_projects\\AutoFormAI\\src\\app\\page.tsx": "2", "C:\\git_projects\\AutoFormAI\\src\\components\\Demo.tsx": "3", "C:\\git_projects\\AutoFormAI\\src\\components\\FAQ.tsx": "4", "C:\\git_projects\\AutoFormAI\\src\\components\\Features.tsx": "5", "C:\\git_projects\\AutoFormAI\\src\\components\\Footer.tsx": "6", "C:\\git_projects\\AutoFormAI\\src\\components\\Header.tsx": "7", "C:\\git_projects\\AutoFormAI\\src\\components\\Hero.tsx": "8", "C:\\git_projects\\AutoFormAI\\src\\components\\HowItWorks.tsx": "9", "C:\\git_projects\\AutoFormAI\\src\\components\\LoadingSpinner.tsx": "10", "C:\\git_projects\\AutoFormAI\\src\\components\\TechStack.tsx": "11", "C:\\git_projects\\AutoFormAI\\src\\components\\ui\\dialog.tsx": "12", "C:\\git_projects\\AutoFormAI\\src\\components\\UseCases.tsx": "13", "C:\\git_projects\\AutoFormAI\\src\\lib\\utils.ts": "14"}, {"size": 1325, "mtime": 1752943466245, "results": "15", "hashOfConfig": "16"}, {"size": 641, "mtime": 1752942283425, "results": "17", "hashOfConfig": "16"}, {"size": 9938, "mtime": 1752942125490, "results": "18", "hashOfConfig": "16"}, {"size": 6383, "mtime": 1752942243943, "results": "19", "hashOfConfig": "16"}, {"size": 5636, "mtime": 1752950123172, "results": "20", "hashOfConfig": "16"}, {"size": 5877, "mtime": 1752950019001, "results": "21", "hashOfConfig": "16"}, {"size": 4576, "mtime": 1752949871270, "results": "22", "hashOfConfig": "16"}, {"size": 7921, "mtime": 1752950136240, "results": "23", "hashOfConfig": "16"}, {"size": 5518, "mtime": 1752943804022, "results": "24", "hashOfConfig": "16"}, {"size": 438, "mtime": 1752943558240, "results": "25", "hashOfConfig": "16"}, {"size": 7337, "mtime": 1752944850525, "results": "26", "hashOfConfig": "16"}, {"size": 3827, "mtime": 1752942082090, "results": "27", "hashOfConfig": "16"}, {"size": 6231, "mtime": 1752943815640, "results": "28", "hashOfConfig": "16"}, {"size": 166, "mtime": 1752941788250, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "s21kuz", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\git_projects\\AutoFormAI\\src\\app\\layout.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\app\\page.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\Demo.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\FAQ.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\Features.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\Footer.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\Header.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\Hero.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\HowItWorks.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\TechStack.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\ui\\dialog.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\components\\UseCases.tsx", [], [], "C:\\git_projects\\AutoFormAI\\src\\lib\\utils.ts", [], []]
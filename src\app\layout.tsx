import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "🧠 AutoForm AI - Chrome Extension for Automatic Form Filling",
  description:
    "Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation.",
  keywords:
    "chrome extension, form filling, AI automation, markdown, productivity, job applications",
  authors: [{ name: "AutoForm AI Team" }],
  openGraph: {
    title: "🧠 AutoForm AI - Chrome Extension for Automatic Form Filling",
    description:
      "Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}

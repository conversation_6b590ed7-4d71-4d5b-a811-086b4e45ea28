# 🧠 AutoForm AI - Landing Page

A viral, modern landing page for the AutoForm AI Chrome extension that automatically fills forms using AI and Markdown files.

## ✨ Features

- **Modern Design**: Clean, minimal design with Tailwind CSS
- **Smooth Animations**: Framer Motion animations for engaging UX
- **Responsive**: Works perfectly on all devices
- **Interactive Demo**: Live demo wizard showing how the extension works
- **Viral Copy**: Funny, relatable copy that converts visitors
- **SEO Optimized**: Proper meta tags and structured data

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **UI Components**: Radix UI
- **TypeScript**: Full type safety

## 🛠️ Getting Started

1. **Install dependencies**

   ```bash
   npm install
   ```

2. **Run the development server**

   ```bash
   npm run dev
   ```

3. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and animations
│   ├── layout.tsx           # Root layout with metadata
│   └── page.tsx             # Main landing page
├── components/
│   ├── Header.tsx           # Sticky navigation header
│   ├── Hero.tsx             # Hero section with CTA
│   ├── Features.tsx         # Features grid
│   ├── HowItWorks.tsx       # Step-by-step guide
│   ├── Demo.tsx             # Interactive demo wizard
│   ├── UseCases.tsx         # Use cases showcase
│   ├── TechStack.tsx        # Technology stack
│   ├── FAQ.tsx              # Frequently asked questions
│   ├── Footer.tsx           # Footer with links
│   └── ui/
│       └── dialog.tsx       # Reusable dialog component
└── lib/
    └── utils.ts             # Utility functions
```

## 🎨 Design Philosophy

- **Viral-First**: Every element designed to be shareable and memorable
- **Conversion-Focused**: Clear CTAs and compelling copy throughout
- **Performance**: Optimized for fast loading and smooth interactions
- **Accessibility**: Proper focus management and screen reader support

## 🚀 Deployment

Deploy easily on Vercel:

```bash
npm run build
```

## 📝 License

MIT License - feel free to use this for your own projects!

---

Made with 💻 by Open Source Humans • Built in India 🇮🇳

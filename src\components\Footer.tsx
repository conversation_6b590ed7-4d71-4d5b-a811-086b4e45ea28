"use client";

import { motion } from "framer-motion";
import { Github, FileText, Chrome, Heart } from "lucide-react";

const Footer = () => {
  const links = [
    { name: "GitH<PERSON>", href: "#", icon: Github },
    { name: "<PERSON><PERSON>", href: "#", icon: FileText },
    { name: "Add to Chrome", href: "#", icon: Chrome },
  ];

  return (
    <footer className="bg-black text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {/* Logo and Description */}
          <div className="md:col-span-1">
            <motion.div
              className="flex items-center space-x-2 mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <span className="text-3xl">🧠</span>
              <span className="text-2xl font-bold">AutoForm AI</span>
            </motion.div>
            <motion.p
              className="text-gray-400 text-lg leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              The Chrome extension that ends the tyranny of online forms
              forever. Because life's too short to copy-paste the same info 47
              times.
            </motion.p>
          </div>

          {/* Quick Links */}
          <div className="md:col-span-1">
            <motion.h3
              className="text-xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Quick Links
            </motion.h3>
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              {links.map((link) => (
                <motion.a
                  key={link.name}
                  href={link.href}
                  className="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors duration-200 group"
                  whileHover={{ x: 5 }}
                >
                  <link.icon className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
                  <span>{link.name}</span>
                </motion.a>
              ))}
            </motion.div>
          </div>

          {/* CTA */}
          <div className="md:col-span-1">
            <motion.h3
              className="text-xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              Ready to Get Started?
            </motion.h3>
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
            >
              <p className="text-gray-400">
                Join the form-filling revolution. Your future self will thank
                you.
              </p>
              <motion.button
                className="w-full bg-white text-black px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center space-x-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Chrome className="w-5 h-5" />
                <span>Add to Chrome</span>
                <span>🔥</span>
              </motion.button>
            </motion.div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-800 my-8"></div>

        {/* Bottom Footer */}
        <motion.div
          className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center space-x-2 text-gray-400">
            <span>Made with</span>
            <Heart className="w-4 h-4 text-red-500" />
            <span>by Open Source Humans</span>
          </div>

          <div className="flex items-center space-x-6 text-gray-400">
            <span>MIT License</span>
            <span>•</span>
            <div className="flex items-center space-x-1">
              <span>Built in India</span>
              <span>🇮🇳</span>
            </div>
          </div>
        </motion.div>

        {/* Fun Easter Egg */}
        <motion.div
          className="text-center mt-8 pt-8 border-t border-gray-800"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="text-gray-500 text-sm">
            "The best form filler is the one you never have to use manually." -
            Ancient Developer Proverb
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;

"use client"

import { motion } from "framer-motion"
import { Download, Upload, Tag, MousePointer, Edit, Send } from "lucide-react"

const HowItWorks = () => {
  const steps = [
    {
      icon: Download,
      title: "Install the Extension",
      description: "Add it to Chrome. Boom. You're 12% more productive already.",
      emoji: "⬇️"
    },
    {
      icon: Upload,
      title: "Drop Your Markdown Files",
      description: "about-me.md, job.md, awkward-college-phase.md — we don't judge.",
      emoji: "📂"
    },
    {
      icon: Tag,
      title: "Tag What Matters",
      description: "Use #resume, #founder-story, #gov-form so AI knows what's up.",
      emoji: "🏷️"
    },
    {
      icon: MousePointer,
      title: "Click 'Fill Form'",
      description: "We crawl the DOM. LLMs whisper answers. You sip chai.",
      emoji: "🖱️"
    },
    {
      icon: Edit,
      title: "Review & Edit",
      description: "Didn't like that answer? Fix it. Or make it more spicy.",
      emoji: "✏️"
    },
    {
      icon: Send,
      title: "Submit with Peace of Mind",
      description: "Your future self thanks you.",
      emoji: "🎯"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="how-it-works" className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            How It Works
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Six simple steps to form-filling enlightenment. 
            It's easier than explaining why you need another Chrome extension.
          </p>
        </motion.div>

        {/* Steps */}
        <motion.div
          className="space-y-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {steps.map((step, index) => (
            <motion.div
              key={index}
              className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group"
              variants={itemVariants}
            >
              {/* Step Number */}
              <div className="flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300">
                {index + 1}
              </div>

              {/* Icon and Content */}
              <div className="flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
                {/* Icon */}
                <div className="flex items-center space-x-3">
                  <span className="text-3xl">{step.emoji}</span>
                  <step.icon className="w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" />
                </div>

                {/* Content */}
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300">
                    {step.title}
                  </h3>
                  <p className="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    {step.description}
                  </p>
                </div>
              </div>

              {/* Connector Line (except for last item) */}
              {index < steps.length - 1 && (
                <div className="hidden md:block absolute left-8 mt-20 w-0.5 h-16 bg-gray-200 group-hover:bg-gray-300 transition-colors duration-300" />
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Section */}
        <motion.div
          className="text-center mt-16 p-8 bg-white rounded-2xl shadow-lg border border-gray-100"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            That's it! 🎉
          </h3>
          <p className="text-lg text-gray-600 mb-6">
            No PhD in computer science required. No sacrificing your firstborn to the form gods.
            Just pure, unadulterated form-filling magic.
          </p>
          <motion.button
            className="inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>Try It Now</span>
            <span>✨</span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default HowItWorks

1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[6996,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
5:I[5720,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
6:I[9429,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
7:I[8781,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
8:I[1030,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
9:I[2300,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
a:I[6492,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
b:I[1851,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
c:I[6821,["409","static/chunks/409-6825d6e2ddffad33.js","974","static/chunks/app/page-ab6467888e6573ad.js"],"default"]
d:I[9665,[],"OutletBoundary"]
f:I[4911,[],"AsyncMetadataOutlet"]
11:I[9665,[],"ViewportBoundary"]
13:I[9665,[],"MetadataBoundary"]
14:"$Sreact.suspense"
16:I[8393,[],""]
:HL["/_next/static/css/e77b9a5b60dfec5e.css","style"]
0:{"P":null,"b":"36RA6dg7Yqnyg-EPl1WLd","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e77b9a5b60dfec5e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen","children":[["$","$L4",null,{}],["$","$L5",null,{}],["$","$L6",null,{}],["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}],["$","$La",null,{}],["$","$Lb",null,{}],["$","$Lc",null,{}]]}],null,["$","$Ld",null,{"children":["$Le",["$","$Lf",null,{"promise":"$@10"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$L11",null,{"children":"$L12"}],null],["$","$L13",null,{"children":["$","div",null,{"hidden":true,"children":["$","$14",null,{"fallback":null,"children":"$L15"}]}]}]]}],false]],"m":"$undefined","G":["$16",[]],"s":false,"S":true}
12:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
e:null
17:I[8175,[],"IconMark"]
10:{"metadata":[["$","title","0",{"children":"🧠 AutoForm AI - Chrome Extension for Automatic Form Filling"}],["$","meta","1",{"name":"description","content":"Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation."}],["$","meta","2",{"name":"author","content":"AutoForm AI Team"}],["$","meta","3",{"name":"keywords","content":"chrome extension, form filling, AI automation, markdown, productivity, job applications"}],["$","meta","4",{"property":"og:title","content":"🧠 AutoForm AI - Chrome Extension for Automatic Form Filling"}],["$","meta","5",{"property":"og:description","content":"Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation."}],["$","meta","6",{"property":"og:type","content":"website"}],["$","meta","7",{"name":"twitter:card","content":"summary"}],["$","meta","8",{"name":"twitter:title","content":"🧠 AutoForm AI - Chrome Extension for Automatic Form Filling"}],["$","meta","9",{"name":"twitter:description","content":"Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation."}],["$","link","10",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L17","11",{}]],"error":null,"digest":"$undefined"}
15:"$10:metadata"

"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Play, Upload, Tag, Sparkles, ArrowRight, ArrowLeft, Check } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

const Demo = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  const demoSteps = [
    {
      title: "Upload Your Markdown",
      description: "Let's start with your about-me.md file",
      content: (
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Drop your about-me.md file here</p>
            <p className="text-sm text-gray-500 mt-2">Or click to browse</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <p className="text-sm text-gray-600 font-mono">
              📄 about-me.md loaded successfully!
            </p>
          </div>
        </div>
      )
    },
    {
      title: "Add Tags",
      description: "Tag your content so AI knows what's up",
      content: (
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {["#resume", "#startup", "#founder-story"].map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-black text-white"
              >
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </span>
            ))}
          </div>
          <input
            type="text"
            placeholder="Add more tags..."
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
          />
        </div>
      )
    },
    {
      title: "AI Magic Happens",
      description: "Watch as AI analyzes the form and suggests answers",
      content: (
        <div className="space-y-4">
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Sparkles className="w-6 h-6 text-purple-600 animate-pulse" />
              <span className="text-lg font-semibold">AI is thinking...</span>
            </div>
            <div className="space-y-2">
              <div className="h-2 bg-purple-200 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-purple-600 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 2, ease: "easeInOut" }}
                />
              </div>
              <p className="text-sm text-gray-600">
                Analyzing form fields... Matching with your content... Generating responses...
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Review & Edit",
      description: "Check the AI suggestions and make any tweaks",
      content: (
        <div className="space-y-4">
          <div className="border border-gray-200 rounded-lg p-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              type="text"
              value="John Doe"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent"
              readOnly
            />
          </div>
          <div className="border border-gray-200 rounded-lg p-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Why do you want this job?
            </label>
            <textarea
              value="I'm passionate about building products that solve real problems. My experience in full-stack development and my love for clean code make me a perfect fit for this role."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent h-20"
              readOnly
            />
          </div>
          <div className="flex items-center space-x-2 text-green-600">
            <Check className="w-4 h-4" />
            <span className="text-sm">All fields filled automatically!</span>
          </div>
        </div>
      )
    },
    {
      title: "Submit with Confidence",
      description: "Your form is ready to go!",
      content: (
        <div className="text-center space-y-6">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <Check className="w-10 h-10 text-green-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Form Completed! 🎉
            </h3>
            <p className="text-gray-600">
              Your application has been filled out perfectly. Time saved: 15 minutes.
              Stress level: Minimal. Chance of typos: Zero.
            </p>
          </div>
          <button className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
            Submit Application
          </button>
        </div>
      )
    }
  ]

  const nextStep = () => {
    if (currentStep < demoSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const resetDemo = () => {
    setCurrentStep(0)
    setIsOpen(false)
  }

  return (
    <section id="demo" className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            See It In Action
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Don't just take our word for it. Watch AutoForm AI work its magic
            on a real job application form. No smoke, no mirrors, just pure automation.
          </p>

          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <motion.button
                className="inline-flex items-center space-x-3 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Play className="w-6 h-6" />
                <span>🧠 Try Smart Fill Demo</span>
              </motion.button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold">
                  {demoSteps[currentStep].title}
                </DialogTitle>
                <DialogDescription className="text-lg">
                  {demoSteps[currentStep].description}
                </DialogDescription>
              </DialogHeader>

              <div className="py-6">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentStep}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {demoSteps[currentStep].content}
                  </motion.div>
                </AnimatePresence>
              </div>

              <div className="flex items-center justify-between">
                <button
                  onClick={prevStep}
                  disabled={currentStep === 0}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>Previous</span>
                </button>

                <div className="flex space-x-2">
                  {demoSteps.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentStep ? "bg-black" : "bg-gray-300"
                      }`}
                    />
                  ))}
                </div>

                {currentStep < demoSteps.length - 1 ? (
                  <button
                    onClick={nextStep}
                    className="flex items-center space-x-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
                  >
                    <span>Next</span>
                    <ArrowRight className="w-4 h-4" />
                  </button>
                ) : (
                  <button
                    onClick={resetDemo}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <span>Try Again</span>
                    <Sparkles className="w-4 h-4" />
                  </button>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </motion.div>
      </div>
    </section>
  )
}

export default Demo

"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON>, FileText, Brain, Eye, Shield } from "lucide-react"

const Features = () => {
  const features = [
    {
      icon: Search,
      title: "Auto-Detects Any Form",
      description: "No matter the form. Even the weird ones your college admin made.",
      emoji: "🔍"
    },
    {
      icon: Bo<PERSON>,
      title: "AI That Actually Gets It",
      description: "Understands the question like a human. But doesn't complain.",
      emoji: "🤖"
    },
    {
      icon: FileText,
      title: "Markdown = Brain",
      description: "Store your story in .md files. Tag them like a boss: #resume #pitch",
      emoji: "📂"
    },
    {
      icon: Brain,
      title: "Context-Aware Engine",
      description: "The more you use it, the smarter it gets. Like a very nerdy Tamagotchi.",
      emoji: "🧠"
    },
    {
      icon: Eye,
      title: "Preview Before You Oops",
      description: "Don't worry, you can edit before submitting that 2AM cover letter.",
      emoji: "✍️"
    },
    {
      icon: Shield,
      title: "Local & API-Key Friendly",
      description: "Your data stays with you. Like a loyal dog. That knows JSO<PERSON>.",
      emoji: "🔐"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="features" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Features That Actually Work
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Because life's too short to fill out forms manually. Let AI do the boring stuff
            while you focus on the important things. Like choosing the perfect GIF.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="group relative bg-gray-50 rounded-2xl p-8 hover:bg-white hover:shadow-xl transition-all duration-300 border border-transparent hover:border-gray-200"
              variants={itemVariants}
              whileHover={{ y: -5 }}
            >
              {/* Icon */}
              <div className="flex items-center space-x-3 mb-4">
                <span className="text-3xl">{feature.emoji}</span>
                <feature.icon className="w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" />
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">
                {feature.title}
              </h3>
              <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                {feature.description}
              </p>

              {/* Hover effect background */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <p className="text-lg text-gray-600 mb-6">
            Ready to never fill out another form manually?
          </p>
          <motion.button
            className="inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>Get Started Now</span>
            <span>🚀</span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Features

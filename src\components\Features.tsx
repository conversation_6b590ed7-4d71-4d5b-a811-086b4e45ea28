"use client";

import { motion } from "framer-motion";
import { FileText, Brain, <PERSON>, <PERSON>, <PERSON>rkles, Target } from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: Target,
      title: "Finds Forms Like Magic",
      description:
        "Spots every form field instantly - job apps, surveys, government forms, you name it. Even the weird ones your college made.",
      gradient: "from-blue-500 to-cyan-500",
      benefit: "Works on 99% of websites",
    },
    {
      icon: <PERSON>rk<PERSON>,
      title: "AI That Reads Your Mind",
      description:
        "Knows when to use your startup pitch vs your dating profile. Context matters, and our AI gets it.",
      gradient: "from-purple-500 to-pink-500",
      benefit: "95% accuracy rate",
    },
    {
      icon: FileText,
      title: "Your Life in Markdown",
      description:
        "Store everything in simple .md files. Tag with #resume, #startup, #personal. Organize like a pro.",
      gradient: "from-green-500 to-emerald-500",
      benefit: "One-time setup",
    },
    {
      icon: Brain,
      title: "Smart Context Switching",
      description:
        "Automatically picks the right info for each form. Job application? Uses professional stuff. Survey? Uses casual tone.",
      gradient: "from-orange-500 to-red-500",
      benefit: "Zero manual selection",
    },
    {
      icon: Eye,
      title: "Preview Before Disaster",
      description:
        "Always shows you what it's about to fill. No embarrassing autocorrect moments or wrong info submissions.",
      gradient: "from-indigo-500 to-purple-500",
      benefit: "100% control",
    },
    {
      icon: Lock,
      title: "Privacy Like Fort Knox",
      description:
        "Everything stays on your computer. No cloud uploads, no data mining, no creepy tracking. Just you and your data.",
      gradient: "from-gray-600 to-gray-800",
      benefit: "Zero data collection",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section id="features" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Stop Wasting 2+ Hours Daily
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            The average person fills 47 forms per month. That's 94 hours of
            copy-pasting the same info.
            <span className="font-semibold text-gray-800">
              {" "}
              We'll get that down to 3 minutes.
            </span>
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="group relative bg-white rounded-2xl p-4 sm:p-6 lg:p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200 h-full flex flex-col"
              variants={itemVariants}
              whileHover={{ y: -4, scale: 1.01 }}
            >
              {/* Background Gradient */}
              <div
                className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
              />

              {/* Icon */}
              <div className="relative z-10 mb-4 sm:mb-6">
                <div
                  className={`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br ${feature.gradient} rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-105 transition-all duration-300`}
                >
                  <feature.icon className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1">
                <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2 sm:mb-3 group-hover:text-black transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-sm sm:text-base text-gray-600 group-hover:text-gray-700 transition-colors duration-300 mb-3 sm:mb-4 leading-relaxed">
                  {feature.description}
                </p>
                <div
                  className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r ${feature.gradient} text-white`}
                >
                  {feature.benefit}
                </div>
              </div>

              {/* Hover effect background */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <p className="text-lg text-gray-600 mb-6">
            Ready to never fill out another form manually?
          </p>
          <motion.button
            className="inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>Get Started Now</span>
            <span>🚀</span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Features;

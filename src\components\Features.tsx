"use client";

import { motion } from "framer-motion";
import { <PERSON>Text, Brain, <PERSON>, <PERSON>, <PERSON>rkles, Target } from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: Target,
      title: "Auto-Detects Any Form",
      description:
        "Intelligently identifies form fields across any website, even complex multi-step forms.",
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      icon: <PERSON>rk<PERSON>,
      title: "AI That Actually Gets It",
      description:
        "Advanced language models understand context and provide relevant, accurate responses.",
      gradient: "from-purple-500 to-pink-500",
    },
    {
      icon: FileText,
      title: "Markdown = Brain",
      description:
        "Store your personal data in organized .md files with smart tagging for instant retrieval.",
      gradient: "from-green-500 to-emerald-500",
    },
    {
      icon: Brain,
      title: "Context-Aware Engine",
      description:
        "Intelligently selects the right information based on form context and your tagged data.",
      gradient: "from-orange-500 to-red-500",
    },
    {
      icon: Eye,
      title: "Preview Before You Oops",
      description:
        "Always gives you control with a preview step before any form submission.",
      gradient: "from-indigo-500 to-purple-500",
    },
    {
      icon: Lock,
      title: "Local & API-Key Friendly",
      description:
        "All processing happens locally. Your personal data never leaves your device.",
      gradient: "from-gray-600 to-gray-800",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section id="features" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Features That Actually Work
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Because life's too short to fill out forms manually. Let AI do the
            boring stuff while you focus on the important things. Like choosing
            the perfect GIF.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="group relative bg-white rounded-3xl p-6 md:p-8 hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 h-full flex flex-col overflow-hidden"
              variants={itemVariants}
              whileHover={{ y: -8, scale: 1.02 }}
            >
              {/* Background Gradient */}
              <div
                className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
              />

              {/* Icon */}
              <div className="relative z-10 mb-6">
                <div
                  className={`w-14 h-14 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300`}
                >
                  <feature.icon className="w-7 h-7 text-white" />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                  {feature.description}
                </p>
              </div>

              {/* Hover effect background */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <p className="text-lg text-gray-600 mb-6">
            Ready to never fill out another form manually?
          </p>
          <motion.button
            className="inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>Get Started Now</span>
            <span>🚀</span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Features;

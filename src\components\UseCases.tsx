"use client"

import { motion } from "framer-motion"
import { Briefcase, Building, Rocket, Calendar } from "lucide-react"

const UseCases = () => {
  const useCases = [
    {
      icon: Briefcase,
      title: "Job Applications",
      description: "resume.md + about-me.md = LinkedIn Autofill on steroids",
      emoji: "💼",
      examples: ["Cover letters", "Application forms", "LinkedIn profiles", "Portfolio submissions"]
    },
    {
      icon: Building,
      title: "Government Forms",
      description: "id.md + address.md = Less pain at 3PM on a Friday",
      emoji: "🏛️",
      examples: ["Tax forms", "Visa applications", "License renewals", "Benefits claims"]
    },
    {
      icon: Rocket,
      title: "Startup Pitches",
      description: "pitch.md + founder.md = One-click YC dreams",
      emoji: "🚀",
      examples: ["Accelerator applications", "Grant proposals", "Investor forms", "Competition entries"]
    },
    {
      icon: Calendar,
      title: "Event Registration",
      description: "socials.md = RSVP like the legend you are",
      emoji: "🎟️",
      examples: ["Conference registration", "Workshop signups", "Meetup RSVPs", "Webinar forms"]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Use Cases That Matter
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From job hunting to government paperwork, AutoForm AI has your back.
            Because life's too short to copy-paste the same info 47 times.
          </p>
        </motion.div>

        {/* Use Cases Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {useCases.map((useCase, index) => (
            <motion.div
              key={index}
              className="group relative bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200"
              variants={itemVariants}
              whileHover={{ y: -5 }}
            >
              {/* Header */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center group-hover:bg-gray-800 transition-colors duration-300">
                  <span className="text-2xl">{useCase.emoji}</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 group-hover:text-black transition-colors duration-300">
                    {useCase.title}
                  </h3>
                  <useCase.icon className="w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300 mt-1" />
                </div>
              </div>

              {/* Description */}
              <p className="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300 mb-6">
                {useCase.description}
              </p>

              {/* Examples */}
              <div className="space-y-2">
                <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wide">
                  Perfect for:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {useCase.examples.map((example, exampleIndex) => (
                    <span
                      key={exampleIndex}
                      className="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300"
                    >
                      {example}
                    </span>
                  ))}
                </div>
              </div>

              {/* Hover effect background */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-r from-gray-900 to-black rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Automate Your Life?
            </h3>
            <p className="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of smart people who've already said goodbye to manual form filling.
              Your future self will thank you.
            </p>
            <motion.button
              className="inline-flex items-center space-x-2 bg-white text-black px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Get AutoForm AI</span>
              <span>🚀</span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default UseCases

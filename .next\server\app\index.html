<!DOCTYPE html><!--KyI83ebJbPthODkWr2VUP--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/c4d3d573209ba8f9.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-f482c466094c0b73.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-540481dc452dbf61.js" async=""></script><script src="/_next/static/chunks/main-app-034781701b3b81f6.js" async=""></script><script src="/_next/static/chunks/488-ed6ad09a10b3fe45.js" async=""></script><script src="/_next/static/chunks/app/page-0d4195da5b480b9d.js" async=""></script><title>🧠 AutoForm AI - Chrome Extension for Automatic Form Filling</title><meta name="description" content="Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation."/><meta name="author" content="AutoForm AI Team"/><meta name="keywords" content="chrome extension, form filling, AI automation, markdown, productivity, job applications"/><meta property="og:title" content="🧠 AutoForm AI - Chrome Extension for Automatic Form Filling"/><meta property="og:description" content="Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation."/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary"/><meta name="twitter:title" content="🧠 AutoForm AI - Chrome Extension for Automatic Form Filling"/><meta name="twitter:description" content="Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen"><header class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 bg-transparent" style="transform:translateY(-100px)"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-16"><div class="flex items-center space-x-3" tabindex="0"><div class="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain w-5 h-5 text-white" aria-hidden="true"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div><span class="text-xl font-bold text-gray-900">AutoForm AI</span></div><nav class="hidden md:flex items-center space-x-8"><a href="#features" class="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors duration-200" tabindex="0"><span>Features</span></a><a href="#how-it-works" class="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors duration-200" tabindex="0"><span>How it works</span></a><a href="#demo" class="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors duration-200" tabindex="0"><span>Demo</span></a><a href="#github" class="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors duration-200" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github w-4 h-4" aria-hidden="true"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg><span>GitHub</span></a></nav><button class="hidden md:flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-2.5 rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 shadow-lg" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download w-4 h-4" aria-hidden="true"><path d="M12 15V3"></path><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><path d="m7 10 5 5 5-5"></path></svg><span>Get Extension</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flame w-4 h-4 text-orange-300" aria-hidden="true"><path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"></path></svg></button><button class="md:hidden p-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu w-6 h-6" aria-hidden="true"><path d="M4 12h16"></path><path d="M4 18h16"></path><path d="M4 6h16"></path></svg></button></div></div></header><section class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden"><div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><div class="loading-skeleton w-32 h-8 mx-auto mb-8 rounded-full"></div><div class="loading-skeleton w-full max-w-4xl h-16 mx-auto mb-6 rounded-lg"></div><div class="loading-skeleton w-full max-w-3xl h-12 mx-auto mb-6 rounded-lg"></div><div class="loading-skeleton w-full max-w-2xl h-6 mx-auto mb-12 rounded"></div><div class="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4"><div class="loading-skeleton w-40 h-12 rounded-full"></div><div class="loading-skeleton w-40 h-12 rounded-full"></div></div></div></section><section id="features" class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Features That Actually Work</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto">Because life&#x27;s too short to fill out forms manually. Let AI do the boring stuff while you focus on the important things. Like choosing the perfect GIF.</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8" style="opacity:0"><div class="group relative bg-white rounded-3xl p-6 md:p-8 hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 h-full flex flex-col overflow-hidden" style="opacity:0;transform:translateY(30px)"><div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-cyan-500 opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div><div class="relative z-10 mb-6"><div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-target w-7 h-7 text-white" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">Auto-Detects Any Form</h3><p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Intelligently identifies form fields across any website, even complex multi-step forms.</p></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-white rounded-3xl p-6 md:p-8 hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 h-full flex flex-col overflow-hidden" style="opacity:0;transform:translateY(30px)"><div class="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-500 opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div><div class="relative z-10 mb-6"><div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-7 h-7 text-white" aria-hidden="true"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">AI That Actually Gets It</h3><p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Advanced language models understand context and provide relevant, accurate responses.</p></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-white rounded-3xl p-6 md:p-8 hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 h-full flex flex-col overflow-hidden" style="opacity:0;transform:translateY(30px)"><div class="absolute inset-0 bg-gradient-to-br from-green-500 to-emerald-500 opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div><div class="relative z-10 mb-6"><div class="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-7 h-7 text-white" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">Markdown = Brain</h3><p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Store your personal data in organized .md files with smart tagging for instant retrieval.</p></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-white rounded-3xl p-6 md:p-8 hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 h-full flex flex-col overflow-hidden" style="opacity:0;transform:translateY(30px)"><div class="absolute inset-0 bg-gradient-to-br from-orange-500 to-red-500 opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div><div class="relative z-10 mb-6"><div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain w-7 h-7 text-white" aria-hidden="true"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">Context-Aware Engine</h3><p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Intelligently selects the right information based on form context and your tagged data.</p></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-white rounded-3xl p-6 md:p-8 hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 h-full flex flex-col overflow-hidden" style="opacity:0;transform:translateY(30px)"><div class="absolute inset-0 bg-gradient-to-br from-indigo-500 to-purple-500 opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div><div class="relative z-10 mb-6"><div class="w-14 h-14 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye w-7 h-7 text-white" aria-hidden="true"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">Preview Before You Oops</h3><p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Always gives you control with a preview step before any form submission.</p></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-white rounded-3xl p-6 md:p-8 hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 h-full flex flex-col overflow-hidden" style="opacity:0;transform:translateY(30px)"><div class="absolute inset-0 bg-gradient-to-br from-gray-600 to-gray-800 opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div><div class="relative z-10 mb-6"><div class="w-14 h-14 bg-gradient-to-br from-gray-600 to-gray-800 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-7 h-7 text-white" aria-hidden="true"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-black transition-colors duration-300">Local &amp; API-Key Friendly</h3><p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">All processing happens locally. Your personal data never leaves your device.</p></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div></div><div class="text-center mt-16" style="opacity:0;transform:translateY(30px)"><p class="text-lg text-gray-600 mb-6">Ready to never fill out another form manually?</p><button class="inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg" tabindex="0"><span>Get Started Now</span><span>🚀</span></button></div></div></section><section id="how-it-works" class="py-20 bg-gradient-to-br from-gray-50 to-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">How It Works</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto">Six simple steps to form-filling enlightenment. It&#x27;s easier than explaining why you need another Chrome extension.</p></div><div class="space-y-8" style="opacity:0"><div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group" style="opacity:0;transform:translateX(-30px)"><div class="flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300">1</div><div class="flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6"><div class="flex items-center space-x-3"><span class="text-3xl">⬇️</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" aria-hidden="true"><path d="M12 15V3"></path><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><path d="m7 10 5 5 5-5"></path></svg></div><div class="flex-1"><h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300">Install the Extension</h3><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Add it to Chrome. Boom. You&#x27;re 12% more productive already.</p></div></div><div class="hidden md:block absolute left-8 mt-20 w-0.5 h-16 bg-gray-200 group-hover:bg-gray-300 transition-colors duration-300"></div></div><div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group" style="opacity:0;transform:translateX(-30px)"><div class="flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300">2</div><div class="flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6"><div class="flex items-center space-x-3"><span class="text-3xl">📂</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-upload w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" aria-hidden="true"><path d="M12 3v12"></path><path d="m17 8-5-5-5 5"></path><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path></svg></div><div class="flex-1"><h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300">Drop Your Markdown Files</h3><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300">about-me.md, job.md, awkward-college-phase.md — we don&#x27;t judge.</p></div></div><div class="hidden md:block absolute left-8 mt-20 w-0.5 h-16 bg-gray-200 group-hover:bg-gray-300 transition-colors duration-300"></div></div><div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group" style="opacity:0;transform:translateX(-30px)"><div class="flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300">3</div><div class="flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6"><div class="flex items-center space-x-3"><span class="text-3xl">🏷️</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" aria-hidden="true"><path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path><circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle></svg></div><div class="flex-1"><h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300">Tag What Matters</h3><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Use #resume, #founder-story, #gov-form so AI knows what&#x27;s up.</p></div></div><div class="hidden md:block absolute left-8 mt-20 w-0.5 h-16 bg-gray-200 group-hover:bg-gray-300 transition-colors duration-300"></div></div><div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group" style="opacity:0;transform:translateX(-30px)"><div class="flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300">4</div><div class="flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6"><div class="flex items-center space-x-3"><span class="text-3xl">🖱️</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mouse-pointer w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" aria-hidden="true"><path d="M12.586 12.586 19 19"></path><path d="M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z"></path></svg></div><div class="flex-1"><h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300">Click &#x27;Fill Form&#x27;</h3><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300">We crawl the DOM. LLMs whisper answers. You sip chai.</p></div></div><div class="hidden md:block absolute left-8 mt-20 w-0.5 h-16 bg-gray-200 group-hover:bg-gray-300 transition-colors duration-300"></div></div><div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group" style="opacity:0;transform:translateX(-30px)"><div class="flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300">5</div><div class="flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6"><div class="flex items-center space-x-3"><span class="text-3xl">✏️</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" aria-hidden="true"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"></path></svg></div><div class="flex-1"><h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300">Review &amp; Edit</h3><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Didn&#x27;t like that answer? Fix it. Or make it more spicy.</p></div></div><div class="hidden md:block absolute left-8 mt-20 w-0.5 h-16 bg-gray-200 group-hover:bg-gray-300 transition-colors duration-300"></div></div><div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 group" style="opacity:0;transform:translateX(-30px)"><div class="flex-shrink-0 w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold group-hover:bg-gray-800 transition-colors duration-300">6</div><div class="flex-1 flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6"><div class="flex items-center space-x-3"><span class="text-3xl">🎯</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300" aria-hidden="true"><path d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"></path><path d="m21.854 2.147-10.94 10.939"></path></svg></div><div class="flex-1"><h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-black transition-colors duration-300">Submit with Peace of Mind</h3><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Your future self thanks you.</p></div></div></div></div><div class="text-center mt-16 p-8 bg-white rounded-2xl shadow-lg border border-gray-100" style="opacity:0;transform:translateY(30px)"><h3 class="text-2xl font-bold text-gray-900 mb-4">That&#x27;s it! 🎉</h3><p class="text-lg text-gray-600 mb-6">No PhD in computer science required. No sacrificing your firstborn to the form gods. Just pure, unadulterated form-filling magic.</p><button class="inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg" tabindex="0"><span>Try It Now</span><span>✨</span></button></div></div></section><section id="demo" class="py-20 bg-gradient-to-br from-blue-50 to-purple-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">See It In Action</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">Don&#x27;t just take our word for it. Watch AutoForm AI work its magic on a real job application form. No smoke, no mirrors, just pure automation.</p><button class="inline-flex items-center space-x-3 bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-800 transition-colors duration-200 shadow-lg" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-_R_75avb_" data-state="closed" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-6 h-6" aria-hidden="true"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><span>🧠 Try Smart Fill Demo</span></button></div></div></section><section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Use Cases That Matter</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto">From job hunting to government paperwork, AutoForm AI has your back. Because life&#x27;s too short to copy-paste the same info 47 times.</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8" style="opacity:0"><div class="group relative bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-black rounded-2xl flex items-center justify-center group-hover:bg-gray-800 transition-colors duration-300"><span class="text-2xl">💼</span></div><div><h3 class="text-2xl font-bold text-gray-900 group-hover:text-black transition-colors duration-300">Job Applications</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-briefcase w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300 mt-1" aria-hidden="true"><path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path><rect width="20" height="14" x="2" y="6" rx="2"></rect></svg></div></div><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300 mb-6">resume.md + about-me.md = LinkedIn Autofill on steroids</p><div class="space-y-2"><h4 class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Perfect for:</h4><div class="flex flex-wrap gap-2"><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Cover letters</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Application forms</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">LinkedIn profiles</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Portfolio submissions</span></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-black rounded-2xl flex items-center justify-center group-hover:bg-gray-800 transition-colors duration-300"><span class="text-2xl">🏛️</span></div><div><h3 class="text-2xl font-bold text-gray-900 group-hover:text-black transition-colors duration-300">Government Forms</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300 mt-1" aria-hidden="true"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg></div></div><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300 mb-6">id.md + address.md = Less pain at 3PM on a Friday</p><div class="space-y-2"><h4 class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Perfect for:</h4><div class="flex flex-wrap gap-2"><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Tax forms</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Visa applications</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">License renewals</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Benefits claims</span></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-black rounded-2xl flex items-center justify-center group-hover:bg-gray-800 transition-colors duration-300"><span class="text-2xl">🚀</span></div><div><h3 class="text-2xl font-bold text-gray-900 group-hover:text-black transition-colors duration-300">Startup Pitches</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rocket w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300 mt-1" aria-hidden="true"><path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path><path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path><path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path><path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path></svg></div></div><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300 mb-6">pitch.md + founder.md = One-click YC dreams</p><div class="space-y-2"><h4 class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Perfect for:</h4><div class="flex flex-wrap gap-2"><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Accelerator applications</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Grant proposals</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Investor forms</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Competition entries</span></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-black rounded-2xl flex items-center justify-center group-hover:bg-gray-800 transition-colors duration-300"><span class="text-2xl">🎟️</span></div><div><h3 class="text-2xl font-bold text-gray-900 group-hover:text-black transition-colors duration-300">Event Registration</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-6 h-6 text-gray-600 group-hover:text-black transition-colors duration-300 mt-1" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></div></div><p class="text-lg text-gray-600 group-hover:text-gray-700 transition-colors duration-300 mb-6">socials.md = RSVP like the legend you are</p><div class="space-y-2"><h4 class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Perfect for:</h4><div class="flex flex-wrap gap-2"><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Conference registration</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Workshop signups</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Meetup RSVPs</span><span class="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-gray-200 transition-colors duration-300">Webinar forms</span></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div></div><div class="text-center mt-16" style="opacity:0;transform:translateY(30px)"><div class="bg-gradient-to-r from-gray-900 to-black rounded-2xl p-8 text-white"><h3 class="text-2xl font-bold mb-4">Ready to Automate Your Life?</h3><p class="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">Join thousands of smart people who&#x27;ve already said goodbye to manual form filling. Your future self will thank you.</p><button class="inline-flex items-center space-x-2 bg-white text-black px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg" tabindex="0"><span>Get AutoForm AI</span><span>🚀</span></button></div></div></div></section><section class="py-20 bg-gradient-to-br from-gray-900 to-black text-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold mb-4">Built with Modern Tech</h2><p class="text-xl text-gray-300 max-w-3xl mx-auto">We use the latest and greatest technologies to make sure AutoForm AI is fast, reliable, and secure. No legacy code, no technical debt, just pure innovation.</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8" style="opacity:0"><div class="group relative bg-gray-800 rounded-2xl p-8 hover:bg-gray-700 transition-all duration-300 border border-gray-700 hover:border-gray-600" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-white rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300"><span class="text-2xl">🎨</span></div><div><h3 class="text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300">UI &amp; Design</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-palette w-6 h-6 text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-1" aria-hidden="true"><path d="M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z"></path><circle cx="13.5" cy="6.5" r=".5" fill="currentColor"></circle><circle cx="17.5" cy="10.5" r=".5" fill="currentColor"></circle><circle cx="6.5" cy="12.5" r=".5" fill="currentColor"></circle><circle cx="8.5" cy="7.5" r=".5" fill="currentColor"></circle></svg></div></div><div class="space-y-4"><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">React</h4><p class="text-sm text-gray-300">For building the UI</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Tailwind CSS</h4><p class="text-sm text-gray-300">For styling that doesn&#x27;t suck</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Framer Motion</h4><p class="text-sm text-gray-300">For animations that wow</p></div></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-gray-800 rounded-2xl p-8 hover:bg-gray-700 transition-all duration-300 border border-gray-700 hover:border-gray-600" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-white rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300"><span class="text-2xl">⚡</span></div><div><h3 class="text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300">Core Engine</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-6 h-6 text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-1" aria-hidden="true"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg></div></div><div class="space-y-4"><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Chrome Extension (MV3)</h4><p class="text-sm text-gray-300">The magic happens here</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Markdown Parser</h4><p class="text-sm text-gray-300">Your .md files, understood</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">DOM Crawler</h4><p class="text-sm text-gray-300">Finds forms like a bloodhound</p></div></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-gray-800 rounded-2xl p-8 hover:bg-gray-700 transition-all duration-300 border border-gray-700 hover:border-gray-600" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-white rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300"><span class="text-2xl">🧠</span></div><div><h3 class="text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300">AI &amp; Processing</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap w-6 h-6 text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-1" aria-hidden="true"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg></div></div><div class="space-y-4"><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Gemini API</h4><p class="text-sm text-gray-300">Google&#x27;s finest AI</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">ChatGPT API</h4><p class="text-sm text-gray-300">OpenAI&#x27;s brain power</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Context Engine</h4><p class="text-sm text-gray-300">Understands what you mean</p></div></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div><div class="group relative bg-gray-800 rounded-2xl p-8 hover:bg-gray-700 transition-all duration-300 border border-gray-700 hover:border-gray-600" style="opacity:0;transform:translateY(30px)"><div class="flex items-center space-x-4 mb-6"><div class="w-16 h-16 bg-white rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300"><span class="text-2xl">🔐</span></div><div><h3 class="text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300">Storage &amp; Privacy</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-6 h-6 text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-1" aria-hidden="true"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path></svg></div></div><div class="space-y-4"><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Chrome localStorage</h4><p class="text-sm text-gray-300">Your data stays local</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">File System Access API</h4><p class="text-sm text-gray-300">Direct file access</p></div></div><div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"><div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div><div><h4 class="font-semibold text-white">Zero Cloud Dependency</h4><p class="text-sm text-gray-300">No servers, no worries</p></div></div></div><div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div></div></div><div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center" style="opacity:0;transform:translateY(30px)"><div class="space-y-2"><div class="text-4xl font-bold text-green-400">100%</div><div class="text-gray-300">Open Source</div></div><div class="space-y-2"><div class="text-4xl font-bold text-blue-400">0</div><div class="text-gray-300">Servers Required</div></div><div class="space-y-2"><div class="text-4xl font-bold text-purple-400">∞</div><div class="text-gray-300">Forms Supported</div></div></div><div class="text-center mt-16" style="opacity:0;transform:translateY(30px)"><div class="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600"><h3 class="text-2xl font-bold mb-4">Open Source &amp; Proud</h3><p class="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">Every line of code is open for inspection. Contribute, fork, or just admire our beautiful architecture. We believe in transparency.</p><button class="inline-flex items-center space-x-2 bg-white text-black px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-5 h-5" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg><span>View on GitHub</span></button></div></div></div></section><section class="py-20 bg-gray-50"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Questions? We&#x27;ve Got Answers.</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto">The most common questions about AutoForm AI, answered with our signature blend of honesty and humor.</p></div><div class="space-y-4"><div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden" style="opacity:0;transform:translateY(20px)"><button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-black rounded-xl flex items-center justify-center"><span class="text-xl">🔐</span></div><div><h3 class="text-xl font-bold text-gray-900 flex items-center space-x-2"><span>Is my data safe?</span></h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-5 h-5 text-gray-500 mt-1" aria-hidden="true"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path></svg></div></div><div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-6 h-6 text-gray-500" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></div></button><div class="overflow-hidden" style="height:0px;opacity:0"><div class="px-8 pb-6"><div class="pl-16"><p class="text-gray-600 text-lg leading-relaxed">Yes. Local-first. Your ex doesn&#x27;t have access. Neither do we. All your data stays on your machine, encrypted and secure. We don&#x27;t have servers to hack because we don&#x27;t have servers.</p></div></div></div></div><div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden" style="opacity:0;transform:translateY(20px)"><button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-black rounded-xl flex items-center justify-center"><span class="text-xl">🔑</span></div><div><h3 class="text-xl font-bold text-gray-900 flex items-center space-x-2"><span>What&#x27;s needed?</span></h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-key w-5 h-5 text-gray-500 mt-1" aria-hidden="true"><path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"></path><path d="m21 2-9.6 9.6"></path><circle cx="7.5" cy="15.5" r="5.5"></circle></svg></div></div><div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-6 h-6 text-gray-500" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></div></button></div><div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden" style="opacity:0;transform:translateY(20px)"><button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-black rounded-xl flex items-center justify-center"><span class="text-xl">🌐</span></div><div><h3 class="text-xl font-bold text-gray-900 flex items-center space-x-2"><span>Where does it work?</span></h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-5 h-5 text-gray-500 mt-1" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg></div></div><div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-6 h-6 text-gray-500" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></div></button></div><div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden" style="opacity:0;transform:translateY(20px)"><button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-black rounded-xl flex items-center justify-center"><span class="text-xl">🐞</span></div><div><h3 class="text-xl font-bold text-gray-900 flex items-center space-x-2"><span>Will it break some forms?</span></h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-5 h-5 text-gray-500 mt-1" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg></div></div><div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-6 h-6 text-gray-500" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></div></button></div></div><div class="text-center mt-16" style="opacity:0;transform:translateY(30px)"><div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200"><h3 class="text-2xl font-bold text-gray-900 mb-4">Still Have Questions?</h3><p class="text-lg text-gray-600 mb-6">We&#x27;re here to help! Reach out to us and we&#x27;ll get back to you faster than you can fill out a contact form manually.</p><div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4"><button class="inline-flex items-center space-x-2 bg-black text-white px-6 py-3 rounded-full font-semibold hover:bg-gray-800 transition-colors duration-200" tabindex="0"><span>Contact Support</span><span>💬</span></button><button class="inline-flex items-center space-x-2 bg-gray-100 text-gray-900 px-6 py-3 rounded-full font-semibold hover:bg-gray-200 transition-colors duration-200" tabindex="0"><span>Read Docs</span><span>📚</span></button></div></div></div></div></section><footer class="bg-black text-white py-16"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"><div class="md:col-span-1"><div class="flex items-center space-x-3 mb-4" style="opacity:0;transform:translateY(20px)"><div class="w-10 h-10 bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain w-6 h-6 text-white" aria-hidden="true"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div><span class="text-2xl font-bold">AutoForm AI</span></div><p class="text-gray-400 text-lg leading-relaxed" style="opacity:0;transform:translateY(20px)">The Chrome extension that ends the tyranny of online forms forever. Because life&#x27;s too short to copy-paste the same info 47 times.</p></div><div class="md:col-span-1"><h3 class="text-xl font-bold mb-6" style="opacity:0;transform:translateY(20px)">Quick Links</h3><div class="space-y-4" style="opacity:0;transform:translateY(20px)"><a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors duration-200 group"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github w-5 h-5 group-hover:scale-110 transition-transform duration-200" aria-hidden="true"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg><span>GitHub</span></a><a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors duration-200 group"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-5 h-5 group-hover:scale-110 transition-transform duration-200" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg><span>Docs</span></a><a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors duration-200 group"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chrome w-5 h-5 group-hover:scale-110 transition-transform duration-200" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="21.17" x2="12" y1="8" y2="8"></line><line x1="3.95" x2="8.54" y1="6.06" y2="14"></line><line x1="10.88" x2="15.46" y1="21.94" y2="14"></line></svg><span>Add to Chrome</span></a></div></div><div class="md:col-span-1"><h3 class="text-xl font-bold mb-6" style="opacity:0;transform:translateY(20px)">Ready to Get Started?</h3><div class="space-y-4" style="opacity:0;transform:translateY(20px)"><p class="text-gray-400">Join the form-filling revolution. Your future self will thank you.</p><button class="w-full bg-white text-black px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center space-x-2" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chrome w-5 h-5" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="21.17" x2="12" y1="8" y2="8"></line><line x1="3.95" x2="8.54" y1="6.06" y2="14"></line><line x1="10.88" x2="15.46" y1="21.94" y2="14"></line></svg><span>Add to Chrome</span><span>🔥</span></button></div></div></div><div class="border-t border-gray-800 my-8"></div><div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0" style="opacity:0;transform:translateY(20px)"><div class="flex items-center space-x-2 text-gray-400"><span>Made with</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4 text-red-500" aria-hidden="true"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg><span>by Open Source Humans</span></div><div class="flex items-center space-x-6 text-gray-400"><span>MIT License</span><span>•</span><div class="flex items-center space-x-2"><span>Built in India</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flag w-4 h-4 text-orange-500" aria-hidden="true"><path d="M4 22V4a1 1 0 0 1 .4-.8A6 6 0 0 1 8 2c3 0 5 2 7.333 2q2 0 3.067-.8A1 1 0 0 1 20 4v10a1 1 0 0 1-.4.8A6 6 0 0 1 16 16c-3 0-5-2-8-2a6 6 0 0 0-4 1.528"></path></svg></div></div></div><div class="text-center mt-8 pt-8 border-t border-gray-800" style="opacity:0"><p class="text-gray-500 text-sm">&quot;The best form filler is the one you never have to use manually.&quot; - Ancient Developer Proverb</p></div></div></footer></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-f482c466094c0b73.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[6996,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\n5:I[5720,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\n6:I[9429,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\n7:I[8781,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\n8:I[1030,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\n9:I[2300,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\na:I[6492,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\nb:I[1851,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\nc:I[6821,[\"488\",\"static/chunks/488-ed6ad09a10b3fe45.js\",\"974\",\"static/chunks/app/page-0d4195da5b480b9d.js\"],\"default\"]\nd:I[9665,[],\"OutletBoundary\"]\nf:I[4911,[],\"AsyncMetadataOutlet\"]\n11:I[9665,[],\"ViewportBoundary\"]\n13:I[9665,[],\"MetadataBoundary\"]\n14:\"$Sreact.suspense\"\n16:I[8393,[],\"\"]\n:HL[\"/_next/static/css/c4d3d573209ba8f9.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"KyI83ebJbPthODkWr2VUP\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/c4d3d573209ba8f9.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[[\"$\",\"$L4\",null,{}],[\"$\",\"$L5\",null,{}],[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{}],[\"$\",\"$L9\",null,{}],[\"$\",\"$La\",null,{}],[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{}]]}],null,[\"$\",\"$Ld\",null,{\"children\":[\"$Le\",[\"$\",\"$Lf\",null,{\"promise\":\"$@10\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L11\",null,{\"children\":\"$L12\"}],null],[\"$\",\"$L13\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$14\",null,{\"fallback\":null,\"children\":\"$L15\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$16\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\ne:null\n"])</script><script>self.__next_f.push([1,"17:I[8175,[],\"IconMark\"]\n"])</script><script>self.__next_f.push([1,"10:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"🧠 AutoForm AI - Chrome Extension for Automatic Form Filling\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"AutoForm AI Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"chrome extension, form filling, AI automation, markdown, productivity, job applications\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"🧠 AutoForm AI - Chrome Extension for Automatic Form Filling\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:description\",\"content\":\"Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation.\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"7\",{\"name\":\"twitter:card\",\"content\":\"summary\"}],[\"$\",\"meta\",\"8\",{\"name\":\"twitter:title\",\"content\":\"🧠 AutoForm AI - Chrome Extension for Automatic Form Filling\"}],[\"$\",\"meta\",\"9\",{\"name\":\"twitter:description\",\"content\":\"Let your words live in Markdown. Let your forms fill themselves. The Chrome extension that ends the tyranny of online forms forever with AI-powered automation.\"}],[\"$\",\"link\",\"10\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L17\",\"11\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"15:\"$10:metadata\"\n"])</script></body></html>
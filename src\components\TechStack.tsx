"use client";

import { motion } from "framer-motion";
import { Code, Palette, Zap, Shield, Globe } from "lucide-react";

const TechStack = () => {
  const techCategories = [
    {
      category: "UI & Design",
      icon: Palette,
      emoji: "🎨",
      technologies: [
        { name: "React", description: "For building the UI" },
        { name: "Tailwind CSS", description: "For styling that doesn't suck" },
        { name: "Framer Motion", description: "For animations that wow" },
      ],
    },
    {
      category: "Core Engine",
      icon: Code,
      emoji: "⚡",
      technologies: [
        {
          name: "Chrome Extension (MV3)",
          description: "The magic happens here",
        },
        { name: "Markdown Parser", description: "Your .md files, understood" },
        { name: "DOM Crawler", description: "Finds forms like a bloodhound" },
      ],
    },
    {
      category: "AI & Processing",
      icon: Zap,
      emoji: "🧠",
      technologies: [
        { name: "Gemini API", description: "Google's finest AI" },
        { name: "ChatGPT API", description: "OpenAI's brain power" },
        { name: "Context Engine", description: "Understands what you mean" },
      ],
    },
    {
      category: "Storage & Privacy",
      icon: Shield,
      emoji: "🔐",
      technologies: [
        { name: "Chrome localStorage", description: "Your data stays local" },
        { name: "File System Access API", description: "Direct file access" },
        {
          name: "Zero Cloud Dependency",
          description: "No servers, no worries",
        },
      ],
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 to-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            Built with Modern Tech
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            We use the latest and greatest technologies to make sure AutoForm AI
            is fast, reliable, and secure. No legacy code, no technical debt,
            just pure innovation.
          </p>
        </motion.div>

        {/* Tech Stack Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {techCategories.map((category, index) => (
            <motion.div
              key={index}
              className="group relative bg-gray-800 rounded-2xl p-8 hover:bg-gray-700 transition-all duration-300 border border-gray-700 hover:border-gray-600"
              variants={itemVariants}
              whileHover={{ y: -5 }}
            >
              {/* Header */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300">
                  <span className="text-2xl">{category.emoji}</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300">
                    {category.category}
                  </h3>
                  <category.icon className="w-6 h-6 text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-1" />
                </div>
              </div>

              {/* Technologies */}
              <div className="space-y-4">
                {category.technologies.map((tech, techIndex) => (
                  <div
                    key={techIndex}
                    className="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg group-hover:bg-gray-600 transition-colors duration-300"
                  >
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-white">{tech.name}</h4>
                      <p className="text-sm text-gray-300">
                        {tech.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Hover effect background */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Stats */}
        <motion.div
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="space-y-2">
            <div className="text-4xl font-bold text-green-400">100%</div>
            <div className="text-gray-300">Open Source</div>
          </div>
          <div className="space-y-2">
            <div className="text-4xl font-bold text-blue-400">0</div>
            <div className="text-gray-300">Servers Required</div>
          </div>
          <div className="space-y-2">
            <div className="text-4xl font-bold text-purple-400">∞</div>
            <div className="text-gray-300">Forms Supported</div>
          </div>
        </motion.div>

        {/* Open Source CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600">
            <h3 className="text-2xl font-bold mb-4">Open Source & Proud</h3>
            <p className="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
              Every line of code is open for inspection. Contribute, fork, or
              just admire our beautiful architecture. We believe in
              transparency.
            </p>
            <motion.button
              className="inline-flex items-center space-x-2 bg-white text-black px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Globe className="w-5 h-5" />
              <span>View on GitHub</span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TechStack;

"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[409],{446:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},760:(t,e,n)=>{n.d(e,{N:()=>v});var r=n(5155),i=n(2115),o=n(869),s=n(2885),a=n(7494),l=n(845),u=n(7351),c=n(1508);class d extends i.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,n=(0,u.s)(t)&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(t){let{children:e,isPresent:n,anchorX:o,root:s}=t,a=(0,i.useId)(),l=(0,i.useRef)(null),u=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:t,height:e,top:r,left:i,right:c}=u.current;if(n||!l.current||!t||!e)return;l.current.dataset.motionPopId=a;let d=document.createElement("style");h&&(d.nonce=h);let p=null!=s?s:document.head;return p.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{p.removeChild(d),p.contains(d)&&p.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:l,sizeRef:u,children:i.cloneElement(e,{ref:l})})}let p=t=>{let{children:e,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:c,mode:d,anchorX:p,root:f}=t,g=(0,s.M)(m),y=(0,i.useId)(),v=!0,b=(0,i.useMemo)(()=>(v=!1,{id:y,initial:n,isPresent:o,custom:u,onExitComplete:t=>{for(let e of(g.set(t,!0),g.values()))if(!e)return;a&&a()},register:t=>(g.set(t,!1),()=>g.delete(t))}),[o,g,a]);return c&&v&&(b={...b}),(0,i.useMemo)(()=>{g.forEach((t,e)=>g.set(e,!1))},[o]),i.useEffect(()=>{o||g.size||!a||a()},[o]),"popLayout"===d&&(e=(0,r.jsx)(h,{isPresent:o,anchorX:p,root:f,children:e})),(0,r.jsx)(l.t.Provider,{value:b,children:e})};function m(){return new Map}var f=n(2082);let g=t=>t.key||"";function y(t){let e=[];return i.Children.forEach(t,t=>{(0,i.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:m="left",root:v}=t,[b,x]=(0,f.xQ)(h),w=(0,i.useMemo)(()=>y(e),[e]),k=h&&!b?[]:w.map(g),A=(0,i.useRef)(!0),E=(0,i.useRef)(w),T=(0,s.M)(()=>new Map),[P,M]=(0,i.useState)(w),[S,C]=(0,i.useState)(w);(0,a.E)(()=>{A.current=!1,E.current=w;for(let t=0;t<S.length;t++){let e=g(S[t]);k.includes(e)?T.delete(e):!0!==T.get(e)&&T.set(e,!1)}},[S,k.length,k.join("-")]);let D=[];if(w!==P){let t=[...w];for(let e=0;e<S.length;e++){let n=S[e],r=g(n);k.includes(r)||(t.splice(e,0,n),D.push(n))}return"wait"===d&&D.length&&(t=D),C(y(t)),M(w),null}let{forceRender:R}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:S.map(t=>{let e=g(t),i=(!h||!!b)&&(w===S||k.includes(e));return(0,r.jsx)(p,{isPresent:i,initial:(!A.current||!!l)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,root:v,onExitComplete:i?void 0:()=>{if(!T.has(e))return;T.set(e,!0);let t=!0;T.forEach(e=>{e||(t=!1)}),t&&(null==R||R(),C(E.current),h&&(null==x||x()),u&&u())},anchorX:m,children:t},e)})})}},845:(t,e,n)=>{n.d(e,{t:()=>r});let r=(0,n(2115).createContext)(null)},869:(t,e,n)=>{n.d(e,{L:()=>r});let r=(0,n(2115).createContext)({})},1508:(t,e,n)=>{n.d(e,{Q:()=>r});let r=(0,n(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1539:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1700:(t,e,n)=>{n.d(e,{bm:()=>eh,UC:()=>eu,VY:()=>ed,hJ:()=>el,ZL:()=>ea,bL:()=>eo,hE:()=>ec,l9:()=>es});var r,i,o,s=n(2115),a=n.t(s,2);function l(t,e,{checkForDefaultPrevented:n=!0}={}){return function(r){if(t?.(r),!1===n||!r.defaultPrevented)return e?.(r)}}function u(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function c(...t){return e=>{let n=!1,r=t.map(t=>{let r=u(t,e);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let e=0;e<r.length;e++){let n=r[e];"function"==typeof n?n():u(t[e],null)}}}}function d(...t){return s.useCallback(c(...t),t)}var h=n(5155),p=globalThis?.document?s.useLayoutEffect:()=>{},m=a[" useId ".trim().toString()]||(()=>void 0),f=0;function g(t){let[e,n]=s.useState(m());return p(()=>{t||n(t=>t??String(f++))},[t]),t||(e?`radix-${e}`:"")}var y=a[" useInsertionEffect ".trim().toString()]||p;Symbol("RADIX:SYNC_STATE");var v=n(7650);function b(t){let e=function(t){let e=s.forwardRef((t,e)=>{let{children:n,...r}=t;if(s.isValidElement(n)){var i;let t,o,a=(i=n,(o=(t=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?i.ref:(o=(t=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?i.props.ref:i.props.ref||i.ref),l=function(t,e){let n={...e};for(let r in e){let i=t[r],o=e[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...t)=>{let e=o(...t);return i(...t),e}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...t,...n}}(r,n.props);return n.type!==s.Fragment&&(l.ref=e?c(e,a):a),s.cloneElement(n,l)}return s.Children.count(n)>1?s.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),n=s.forwardRef((t,n)=>{let{children:r,...i}=t,o=s.Children.toArray(r),a=o.find(w);if(a){let t=a.props.children,r=o.map(e=>e!==a?e:s.Children.count(t)>1?s.Children.only(null):s.isValidElement(t)?t.props.children:null);return(0,h.jsx)(e,{...i,ref:n,children:s.isValidElement(t)?s.cloneElement(t,void 0,r):null})}return(0,h.jsx)(e,{...i,ref:n,children:r})});return n.displayName=`${t}.Slot`,n}var x=Symbol("radix.slottable");function w(t){return s.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===x}var k=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let n=b(`Primitive.${e}`),r=s.forwardRef((t,r)=>{let{asChild:i,...o}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,h.jsx)(i?n:e,{...o,ref:r})});return r.displayName=`Primitive.${e}`,{...t,[e]:r}},{});function A(t){let e=s.useRef(t);return s.useEffect(()=>{e.current=t}),s.useMemo(()=>(...t)=>e.current?.(...t),[])}var E="dismissableLayer.update",T=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),P=s.forwardRef((t,e)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:p,onDismiss:m,...f}=t,g=s.useContext(T),[y,v]=s.useState(null),b=null!=(r=null==y?void 0:y.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=s.useState({}),w=d(e,t=>v(t)),P=Array.from(g.layers),[C]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),D=P.indexOf(C),R=y?P.indexOf(y):-1,j=g.layersWithOutsidePointerEventsDisabled.size>0,V=R>=D,L=function(t){var e;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(e=globalThis)?void 0:e.document,r=A(t),i=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{let t=t=>{if(t.target&&!i.current){let e=function(){S("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:t};"touch"===t.pointerType?(n.removeEventListener("click",o.current),o.current=e,n.addEventListener("click",o.current,{once:!0})):e()}else n.removeEventListener("click",o.current);i.current=!1},e=window.setTimeout(()=>{n.addEventListener("pointerdown",t)},0);return()=>{window.clearTimeout(e),n.removeEventListener("pointerdown",t),n.removeEventListener("click",o.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(t=>{let e=t.target,n=[...g.branches].some(t=>t.contains(e));V&&!n&&(null==u||u(t),null==p||p(t),t.defaultPrevented||null==m||m())},b),O=function(t){var e;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(e=globalThis)?void 0:e.document,r=A(t),i=s.useRef(!1);return s.useEffect(()=>{let t=t=>{t.target&&!i.current&&S("dismissableLayer.focusOutside",r,{originalEvent:t},{discrete:!1})};return n.addEventListener("focusin",t),()=>n.removeEventListener("focusin",t)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(t=>{let e=t.target;![...g.branches].some(t=>t.contains(e))&&(null==c||c(t),null==p||p(t),t.defaultPrevented||null==m||m())},b);return!function(t,e=globalThis?.document){let n=A(t);s.useEffect(()=>{let t=t=>{"Escape"===t.key&&n(t)};return e.addEventListener("keydown",t,{capture:!0}),()=>e.removeEventListener("keydown",t,{capture:!0})},[n,e])}(t=>{R===g.layers.size-1&&(null==a||a(t),!t.defaultPrevented&&m&&(t.preventDefault(),m()))},b),s.useEffect(()=>{if(y)return o&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(i=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(y)),g.layers.add(y),M(),()=>{o&&1===g.layersWithOutsidePointerEventsDisabled.size&&(b.body.style.pointerEvents=i)}},[y,b,o,g]),s.useEffect(()=>()=>{y&&(g.layers.delete(y),g.layersWithOutsidePointerEventsDisabled.delete(y),M())},[y,g]),s.useEffect(()=>{let t=()=>x({});return document.addEventListener(E,t),()=>document.removeEventListener(E,t)},[]),(0,h.jsx)(k.div,{...f,ref:w,style:{pointerEvents:j?V?"auto":"none":void 0,...t.style},onFocusCapture:l(t.onFocusCapture,O.onFocusCapture),onBlurCapture:l(t.onBlurCapture,O.onBlurCapture),onPointerDownCapture:l(t.onPointerDownCapture,L.onPointerDownCapture)})});function M(){let t=new CustomEvent(E);document.dispatchEvent(t)}function S(t,e,n,r){let{discrete:i}=r,o=n.originalEvent.target,s=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:n});if(e&&o.addEventListener(t,e,{once:!0}),i)o&&v.flushSync(()=>o.dispatchEvent(s));else o.dispatchEvent(s)}P.displayName="DismissableLayer",s.forwardRef((t,e)=>{let n=s.useContext(T),r=s.useRef(null),i=d(e,r);return s.useEffect(()=>{let t=r.current;if(t)return n.branches.add(t),()=>{n.branches.delete(t)}},[n.branches]),(0,h.jsx)(k.div,{...t,ref:i})}).displayName="DismissableLayerBranch";var C="focusScope.autoFocusOnMount",D="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},j=s.forwardRef((t,e)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:o,...a}=t,[l,u]=s.useState(null),c=A(i),p=A(o),m=s.useRef(null),f=d(e,t=>u(t)),g=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let t=function(t){if(g.paused||!l)return;let e=t.target;l.contains(e)?m.current=e:O(m.current,{select:!0})},e=function(t){if(g.paused||!l)return;let e=t.relatedTarget;null!==e&&(l.contains(e)||O(m.current,{select:!0}))};document.addEventListener("focusin",t),document.addEventListener("focusout",e);let n=new MutationObserver(function(t){if(document.activeElement===document.body)for(let e of t)e.removedNodes.length>0&&O(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",t),document.removeEventListener("focusout",e),n.disconnect()}}},[r,l,g.paused]),s.useEffect(()=>{if(l){F.add(g);let t=document.activeElement;if(!l.contains(t)){let e=new CustomEvent(C,R);l.addEventListener(C,c),l.dispatchEvent(e),e.defaultPrevented||(function(t){let{select:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of t)if(O(r,{select:e}),document.activeElement!==n)return}(V(l).filter(t=>"A"!==t.tagName),{select:!0}),document.activeElement===t&&O(l))}return()=>{l.removeEventListener(C,c),setTimeout(()=>{let e=new CustomEvent(D,R);l.addEventListener(D,p),l.dispatchEvent(e),e.defaultPrevented||O(null!=t?t:document.body,{select:!0}),l.removeEventListener(D,p),F.remove(g)},0)}}},[l,c,p,g]);let y=s.useCallback(t=>{if(!n&&!r||g.paused)return;let e="Tab"===t.key&&!t.altKey&&!t.ctrlKey&&!t.metaKey,i=document.activeElement;if(e&&i){let e=t.currentTarget,[r,o]=function(t){let e=V(t);return[L(e,t),L(e.reverse(),t)]}(e);r&&o?t.shiftKey||i!==o?t.shiftKey&&i===r&&(t.preventDefault(),n&&O(o,{select:!0})):(t.preventDefault(),n&&O(r,{select:!0})):i===e&&t.preventDefault()}},[n,r,g.paused]);return(0,h.jsx)(k.div,{tabIndex:-1,...a,ref:f,onKeyDown:y})});function V(t){let e=[],n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:t=>{let e="INPUT"===t.tagName&&"hidden"===t.type;return t.disabled||t.hidden||e?NodeFilter.FILTER_SKIP:t.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)e.push(n.currentNode);return e}function L(t,e){for(let n of t)if(!function(t,e){let{upTo:n}=e;if("hidden"===getComputedStyle(t).visibility)return!0;for(;t&&(void 0===n||t!==n);){if("none"===getComputedStyle(t).display)return!0;t=t.parentElement}return!1}(n,{upTo:e}))return n}function O(t){let{select:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t&&t.focus){var n;let r=document.activeElement;t.focus({preventScroll:!0}),t!==r&&(n=t)instanceof HTMLInputElement&&"select"in n&&e&&t.select()}}j.displayName="FocusScope";var F=function(){let t=[];return{add(e){let n=t[0];e!==n&&(null==n||n.pause()),(t=N(t,e)).unshift(e)},remove(e){var n;null==(n=(t=N(t,e))[0])||n.resume()}}}();function N(t,e){let n=[...t],r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}var I=s.forwardRef((t,e)=>{var n,r;let{container:i,...o}=t,[a,l]=s.useState(!1);p(()=>l(!0),[]);let u=i||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return u?v.createPortal((0,h.jsx)(k.div,{...o,ref:e}),u):null});I.displayName="Portal";var B=t=>{let{present:e,children:n}=t,r=function(t){var e,n;let[r,i]=s.useState(),o=s.useRef(null),a=s.useRef(t),l=s.useRef("none"),[u,c]=(e=t?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((t,e)=>{let r=n[t][e];return null!=r?r:t},e));return s.useEffect(()=>{let t=z(o.current);l.current="mounted"===u?t:"none"},[u]),p(()=>{let e=o.current,n=a.current;if(n!==t){let r=l.current,i=z(e);t?c("MOUNT"):"none"===i||(null==e?void 0:e.display)==="none"?c("UNMOUNT"):n&&r!==i?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=t}},[t,c]),p(()=>{if(r){var t;let e,n=null!=(t=r.ownerDocument.defaultView)?t:window,i=t=>{let i=z(o.current).includes(t.animationName);if(t.target===r&&i&&(c("ANIMATION_END"),!a.current)){let t=r.style.animationFillMode;r.style.animationFillMode="forwards",e=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=t)})}},s=t=>{t.target===r&&(l.current=z(o.current))};return r.addEventListener("animationstart",s),r.addEventListener("animationcancel",i),r.addEventListener("animationend",i),()=>{n.clearTimeout(e),r.removeEventListener("animationstart",s),r.removeEventListener("animationcancel",i),r.removeEventListener("animationend",i)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:s.useCallback(t=>{o.current=t?getComputedStyle(t):null,i(t)},[])}}(e),i="function"==typeof n?n({present:r.isPresent}):s.Children.only(n),o=d(r.ref,function(t){var e,n;let r=null==(e=Object.getOwnPropertyDescriptor(t.props,"ref"))?void 0:e.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?t.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(t,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?t.props.ref:t.props.ref||t.ref}(i));return"function"==typeof n||r.isPresent?s.cloneElement(i,{ref:o}):null};function z(t){return(null==t?void 0:t.animationName)||"none"}B.displayName="Presence";var U=0;function W(){let t=document.createElement("span");return t.setAttribute("data-radix-focus-guard",""),t.tabIndex=0,t.style.outline="none",t.style.opacity="0",t.style.position="fixed",t.style.pointerEvents="none",t}var $=function(){return($=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function _(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)0>e.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n}Object.create;Object.create;var H=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),q="width-before-scroll-bar";function Y(t,e){return"function"==typeof t?t(e):t&&(t.current=e),t}var X="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,K=new WeakMap;function G(t){return t}var Z=function(t){void 0===t&&(t={});var e,n,r,i=(void 0===e&&(e=G),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(t){var i=e(t,r);return n.push(i),function(){n=n.filter(function(t){return t!==i})}},assignSyncMedium:function(t){for(r=!0;n.length;){var e=n;n=[],e.forEach(t)}n={push:function(e){return t(e)},filter:function(){return n}}},assignMedium:function(t){r=!0;var e=[];if(n.length){var i=n;n=[],i.forEach(t),e=n}var o=function(){var n=e;e=[],n.forEach(t)},s=function(){return Promise.resolve().then(o)};s(),n={push:function(t){e.push(t),s()},filter:function(t){return e=e.filter(t),n}}}});return i.options=$({async:!0,ssr:!1},t),i}(),Q=function(){},J=s.forwardRef(function(t,e){var n,r,i,o,a=s.useRef(null),l=s.useState({onScrollCapture:Q,onWheelCapture:Q,onTouchMoveCapture:Q}),u=l[0],c=l[1],d=t.forwardProps,h=t.children,p=t.className,m=t.removeScrollBar,f=t.enabled,g=t.shards,y=t.sideCar,v=t.noRelative,b=t.noIsolation,x=t.inert,w=t.allowPinchZoom,k=t.as,A=t.gapMode,E=_(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[a,e],r=function(t){return n.forEach(function(e){return Y(e,t)})},(i=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var t=i.value;t!==value&&(i.value=value,i.callback(value,t))}}}})[0]).callback=r,o=i.facade,X(function(){var t=K.get(o);if(t){var e=new Set(t),r=new Set(n),i=o.current;e.forEach(function(t){r.has(t)||Y(t,null)}),r.forEach(function(t){e.has(t)||Y(t,i)})}K.set(o,n)},[n]),o),P=$($({},E),u);return s.createElement(s.Fragment,null,f&&s.createElement(y,{sideCar:Z,removeScrollBar:m,shards:g,noRelative:v,noIsolation:b,inert:x,setCallbacks:c,allowPinchZoom:!!w,lockRef:a,gapMode:A}),d?s.cloneElement(s.Children.only(h),$($({},P),{ref:T})):s.createElement(void 0===k?"div":k,$({},P,{className:p,ref:T}),h))});J.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},J.classNames={fullWidth:q,zeroRight:H};var tt=function(t){var e=t.sideCar,n=_(t,["sideCar"]);if(!e)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=e.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,$({},n))};tt.isSideCarExport=!0;var te=function(){var t=0,e=null;return{add:function(r){if(0==t&&(e=function(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var e=o||n.nc;return e&&t.setAttribute("nonce",e),t}())){var i,s;(i=e).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),s=e,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}t++},remove:function(){--t||!e||(e.parentNode&&e.parentNode.removeChild(e),e=null)}}},tn=function(){var t=te();return function(e,n){s.useEffect(function(){return t.add(e),function(){t.remove()}},[e&&n])}},tr=function(){var t=tn();return function(e){return t(e.styles,e.dynamic),null}},ti={left:0,top:0,right:0,gap:0},to=function(t){return parseInt(t||"",10)||0},ts=function(t){var e=window.getComputedStyle(document.body),n=e["padding"===t?"paddingLeft":"marginLeft"],r=e["padding"===t?"paddingTop":"marginTop"],i=e["padding"===t?"paddingRight":"marginRight"];return[to(n),to(r),to(i)]},ta=function(t){if(void 0===t&&(t="margin"),"undefined"==typeof window)return ti;var e=ts(t),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:e[0],top:e[1],right:e[2],gap:Math.max(0,r-n+e[2]-e[0])}},tl=tr(),tu="data-scroll-locked",tc=function(t,e,n,r){var i=t.left,o=t.top,s=t.right,a=t.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(tu,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([e&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(H," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(q," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(H," .").concat(H," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(q," .").concat(q," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(tu,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},td=function(){var t=parseInt(document.body.getAttribute(tu)||"0",10);return isFinite(t)?t:0},th=function(){s.useEffect(function(){return document.body.setAttribute(tu,(td()+1).toString()),function(){var t=td()-1;t<=0?document.body.removeAttribute(tu):document.body.setAttribute(tu,t.toString())}},[])},tp=function(t){var e=t.noRelative,n=t.noImportant,r=t.gapMode,i=void 0===r?"margin":r;th();var o=s.useMemo(function(){return ta(i)},[i]);return s.createElement(tl,{styles:tc(o,!e,i,n?"":"!important")})},tm=!1;if("undefined"!=typeof window)try{var tf=Object.defineProperty({},"passive",{get:function(){return tm=!0,!0}});window.addEventListener("test",tf,tf),window.removeEventListener("test",tf,tf)}catch(t){tm=!1}var tg=!!tm&&{passive:!1},ty=function(t,e){if(!(t instanceof Element))return!1;var n=window.getComputedStyle(t);return"hidden"!==n[e]&&(n.overflowY!==n.overflowX||"TEXTAREA"===t.tagName||"visible"!==n[e])},tv=function(t,e){var n=e.ownerDocument,r=e;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),tb(t,r)){var i=tx(t,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},tb=function(t,e){return"v"===t?ty(e,"overflowY"):ty(e,"overflowX")},tx=function(t,e){return"v"===t?[e.scrollTop,e.scrollHeight,e.clientHeight]:[e.scrollLeft,e.scrollWidth,e.clientWidth]},tw=function(t,e,n,r,i){var o,s=(o=window.getComputedStyle(e).direction,"h"===t&&"rtl"===o?-1:1),a=s*r,l=n.target,u=e.contains(l),c=!1,d=a>0,h=0,p=0;do{if(!l)break;var m=tx(t,l),f=m[0],g=m[1]-m[2]-s*f;(f||g)&&tb(t,l)&&(h+=g,p+=f);var y=l.parentNode;l=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&l!==document.body||u&&(e.contains(l)||e===l));return d&&(i&&1>Math.abs(h)||!i&&a>h)?c=!0:!d&&(i&&1>Math.abs(p)||!i&&-a>p)&&(c=!0),c},tk=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},tA=function(t){return[t.deltaX,t.deltaY]},tE=function(t){return t&&"current"in t?t.current:t},tT=0,tP=[];let tM=(r=function(t){var e=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),i=s.useState(tT++)[0],o=s.useState(tr)[0],a=s.useRef(t);s.useEffect(function(){a.current=t},[t]),s.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=(function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))})([t.lockRef.current],(t.shards||[]).map(tE),!0).filter(Boolean);return e.forEach(function(t){return t.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach(function(t){return t.classList.remove("allow-interactivity-".concat(i))})}}},[t.inert,t.lockRef.current,t.shards]);var l=s.useCallback(function(t,e){if("touches"in t&&2===t.touches.length||"wheel"===t.type&&t.ctrlKey)return!a.current.allowPinchZoom;var i,o=tk(t),s=n.current,l="deltaX"in t?t.deltaX:s[0]-o[0],u="deltaY"in t?t.deltaY:s[1]-o[1],c=t.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in t&&"h"===d&&"range"===c.type)return!1;var h=tv(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=tv(d,c)),!h)return!1;if(!r.current&&"changedTouches"in t&&(l||u)&&(r.current=i),!i)return!0;var p=r.current||i;return tw(p,e,t,"h"===p?l:u,!0)},[]),u=s.useCallback(function(t){if(tP.length&&tP[tP.length-1]===o){var n="deltaY"in t?tA(t):tk(t),r=e.current.filter(function(e){var r;return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(r=e.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){t.cancelable&&t.preventDefault();return}if(!r){var i=(a.current.shards||[]).map(tE).filter(Boolean).filter(function(e){return e.contains(t.target)});(i.length>0?l(t,i[0]):!a.current.noIsolation)&&t.cancelable&&t.preventDefault()}}},[]),c=s.useCallback(function(t,n,r,i){var o={name:t,delta:n,target:r,should:i,shadowParent:function(t){for(var e=null;null!==t;)t instanceof ShadowRoot&&(e=t.host,t=t.host),t=t.parentNode;return e}(r)};e.current.push(o),setTimeout(function(){e.current=e.current.filter(function(t){return t!==o})},1)},[]),d=s.useCallback(function(t){n.current=tk(t),r.current=void 0},[]),h=s.useCallback(function(e){c(e.type,tA(e),e.target,l(e,t.lockRef.current))},[]),p=s.useCallback(function(e){c(e.type,tk(e),e.target,l(e,t.lockRef.current))},[]);s.useEffect(function(){return tP.push(o),t.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",u,tg),document.addEventListener("touchmove",u,tg),document.addEventListener("touchstart",d,tg),function(){tP=tP.filter(function(t){return t!==o}),document.removeEventListener("wheel",u,tg),document.removeEventListener("touchmove",u,tg),document.removeEventListener("touchstart",d,tg)}},[]);var m=t.removeScrollBar,f=t.inert;return s.createElement(s.Fragment,null,f?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,m?s.createElement(tp,{noRelative:t.noRelative,gapMode:t.gapMode}):null)},Z.useMedium(r),tt);var tS=s.forwardRef(function(t,e){return s.createElement(J,$({},t,{ref:e,sideCar:tM}))});tS.classNames=J.classNames;var tC=new WeakMap,tD=new WeakMap,tR={},tj=0,tV=function(t){return t&&(t.host||tV(t.parentNode))},tL=function(t,e,n,r){var i=(Array.isArray(t)?t:[t]).map(function(t){if(e.contains(t))return t;var n=tV(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(t){return!!t});tR[n]||(tR[n]=new WeakMap);var o=tR[n],s=[],a=new Set,l=new Set(i),u=function(t){!t||a.has(t)||(a.add(t),u(t.parentNode))};i.forEach(u);var c=function(t){!t||l.has(t)||Array.prototype.forEach.call(t.children,function(t){if(a.has(t))c(t);else try{var e=t.getAttribute(r),i=null!==e&&"false"!==e,l=(tC.get(t)||0)+1,u=(o.get(t)||0)+1;tC.set(t,l),o.set(t,u),s.push(t),1===l&&i&&tD.set(t,!0),1===u&&t.setAttribute(n,"true"),i||t.setAttribute(r,"true")}catch(e){console.error("aria-hidden: cannot operate on ",t,e)}})};return c(e),a.clear(),tj++,function(){s.forEach(function(t){var e=tC.get(t)-1,i=o.get(t)-1;tC.set(t,e),o.set(t,i),e||(tD.has(t)||t.removeAttribute(r),tD.delete(t)),i||t.removeAttribute(n)}),--tj||(tC=new WeakMap,tC=new WeakMap,tD=new WeakMap,tR={})}},tO=function(t,e,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(t)?t:[t]),i=e||("undefined"==typeof document?null:(Array.isArray(t)?t[0]:t).ownerDocument.body);return i?(r.push.apply(r,Array.from(i.querySelectorAll("[aria-live], script"))),tL(r,i,n,"aria-hidden")):function(){return null}},tF="Dialog",[tN,tI]=function(t,e=[]){let n=[],r=()=>{let e=n.map(t=>s.createContext(t));return function(n){let r=n?.[t]||e;return s.useMemo(()=>({[`__scope${t}`]:{...n,[t]:r}}),[n,r])}};return r.scopeName=t,[function(e,r){let i=s.createContext(r),o=n.length;n=[...n,r];let a=e=>{let{scope:n,children:r,...a}=e,l=n?.[t]?.[o]||i,u=s.useMemo(()=>a,Object.values(a));return(0,h.jsx)(l.Provider,{value:u,children:r})};return a.displayName=e+"Provider",[a,function(n,a){let l=a?.[t]?.[o]||i,u=s.useContext(l);if(u)return u;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let n=()=>{let n=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let r=n.reduce((e,{useScope:n,scopeName:r})=>{let i=n(t)[`__scope${r}`];return{...e,...i}},{});return s.useMemo(()=>({[`__scope${e.scopeName}`]:r}),[r])}};return n.scopeName=e.scopeName,n}(r,...e)]}(tF),[tB,tz]=tN(tF),tU=t=>{let{__scopeDialog:e,children:n,open:r,defaultOpen:i,onOpenChange:o,modal:a=!0}=t,l=s.useRef(null),u=s.useRef(null),[c,d]=function({prop:t,defaultProp:e,onChange:n=()=>{},caller:r}){let[i,o,a]=function({defaultProp:t,onChange:e}){let[n,r]=s.useState(t),i=s.useRef(n),o=s.useRef(e);return y(()=>{o.current=e},[e]),s.useEffect(()=>{i.current!==n&&(o.current?.(n),i.current=n)},[n,i]),[n,r,o]}({defaultProp:e,onChange:n}),l=void 0!==t,u=l?t:i;{let e=s.useRef(void 0!==t);s.useEffect(()=>{let t=e.current;if(t!==l){let e=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${t?"controlled":"uncontrolled"} to ${e}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}e.current=l},[l,r])}return[u,s.useCallback(e=>{if(l){let n="function"==typeof e?e(t):e;n!==t&&a.current?.(n)}else o(e)},[l,t,o,a])]}({prop:r,defaultProp:null!=i&&i,onChange:o,caller:tF});return(0,h.jsx)(tB,{scope:e,triggerRef:l,contentRef:u,contentId:g(),titleId:g(),descriptionId:g(),open:c,onOpenChange:d,onOpenToggle:s.useCallback(()=>d(t=>!t),[d]),modal:a,children:n})};tU.displayName=tF;var tW="DialogTrigger",t$=s.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,i=tz(tW,n),o=d(e,i.triggerRef);return(0,h.jsx)(k.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":t7(i.open),...r,ref:o,onClick:l(t.onClick,i.onOpenToggle)})});t$.displayName=tW;var t_="DialogPortal",[tH,tq]=tN(t_,{forceMount:void 0}),tY=t=>{let{__scopeDialog:e,forceMount:n,children:r,container:i}=t,o=tz(t_,e);return(0,h.jsx)(tH,{scope:e,forceMount:n,children:s.Children.map(r,t=>(0,h.jsx)(B,{present:n||o.open,children:(0,h.jsx)(I,{asChild:!0,container:i,children:t})}))})};tY.displayName=t_;var tX="DialogOverlay",tK=s.forwardRef((t,e)=>{let n=tq(tX,t.__scopeDialog),{forceMount:r=n.forceMount,...i}=t,o=tz(tX,t.__scopeDialog);return o.modal?(0,h.jsx)(B,{present:r||o.open,children:(0,h.jsx)(tZ,{...i,ref:e})}):null});tK.displayName=tX;var tG=b("DialogOverlay.RemoveScroll"),tZ=s.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,i=tz(tX,n);return(0,h.jsx)(tS,{as:tG,allowPinchZoom:!0,shards:[i.contentRef],children:(0,h.jsx)(k.div,{"data-state":t7(i.open),...r,ref:e,style:{pointerEvents:"auto",...r.style}})})}),tQ="DialogContent",tJ=s.forwardRef((t,e)=>{let n=tq(tQ,t.__scopeDialog),{forceMount:r=n.forceMount,...i}=t,o=tz(tQ,t.__scopeDialog);return(0,h.jsx)(B,{present:r||o.open,children:o.modal?(0,h.jsx)(t0,{...i,ref:e}):(0,h.jsx)(t1,{...i,ref:e})})});tJ.displayName=tQ;var t0=s.forwardRef((t,e)=>{let n=tz(tQ,t.__scopeDialog),r=s.useRef(null),i=d(e,n.contentRef,r);return s.useEffect(()=>{let t=r.current;if(t)return tO(t)},[]),(0,h.jsx)(t2,{...t,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(t.onCloseAutoFocus,t=>{var e;t.preventDefault(),null==(e=n.triggerRef.current)||e.focus()}),onPointerDownOutside:l(t.onPointerDownOutside,t=>{let e=t.detail.originalEvent,n=0===e.button&&!0===e.ctrlKey;(2===e.button||n)&&t.preventDefault()}),onFocusOutside:l(t.onFocusOutside,t=>t.preventDefault())})}),t1=s.forwardRef((t,e)=>{let n=tz(tQ,t.__scopeDialog),r=s.useRef(!1),i=s.useRef(!1);return(0,h.jsx)(t2,{...t,ref:e,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{var o,s;null==(o=t.onCloseAutoFocus)||o.call(t,e),e.defaultPrevented||(r.current||null==(s=n.triggerRef.current)||s.focus(),e.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:e=>{var o,s;null==(o=t.onInteractOutside)||o.call(t,e),e.defaultPrevented||(r.current=!0,"pointerdown"===e.detail.originalEvent.type&&(i.current=!0));let a=e.target;(null==(s=n.triggerRef.current)?void 0:s.contains(a))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&i.current&&e.preventDefault()}})}),t2=s.forwardRef((t,e)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:o,...a}=t,l=tz(tQ,n),u=s.useRef(null),c=d(e,u);return s.useEffect(()=>{var t,e;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(t=n[0])?t:W()),document.body.insertAdjacentElement("beforeend",null!=(e=n[1])?e:W()),U++,()=>{1===U&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),U--}},[]),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(j,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,h.jsx)(P,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":t7(l.open),...a,ref:c,onDismiss:()=>l.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(er,{titleId:l.titleId}),(0,h.jsx)(ei,{contentRef:u,descriptionId:l.descriptionId})]})]})}),t5="DialogTitle",t4=s.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,i=tz(t5,n);return(0,h.jsx)(k.h2,{id:i.titleId,...r,ref:e})});t4.displayName=t5;var t3="DialogDescription",t6=s.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,i=tz(t3,n);return(0,h.jsx)(k.p,{id:i.descriptionId,...r,ref:e})});t6.displayName=t3;var t9="DialogClose",t8=s.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,i=tz(t9,n);return(0,h.jsx)(k.button,{type:"button",...r,ref:e,onClick:l(t.onClick,()=>i.onOpenChange(!1))})});function t7(t){return t?"open":"closed"}t8.displayName=t9;var et="DialogTitleWarning",[ee,en]=function(t,e){let n=s.createContext(e),r=t=>{let{children:e,...r}=t,i=s.useMemo(()=>r,Object.values(r));return(0,h.jsx)(n.Provider,{value:i,children:e})};return r.displayName=t+"Provider",[r,function(r){let i=s.useContext(n);if(i)return i;if(void 0!==e)return e;throw Error(`\`${r}\` must be used within \`${t}\``)}]}(et,{contentName:tQ,titleName:t5,docsSlug:"dialog"}),er=t=>{let{titleId:e}=t,n=en(et),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},ei=t=>{let{contentRef:e,descriptionId:n}=t,r=en("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return s.useEffect(()=>{var t;let r=null==(t=e.current)?void 0:t.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,e,n]),null},eo=tU,es=t$,ea=tY,el=tK,eu=tJ,ec=t4,ed=t6,eh=t8},1788:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1976:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2082:(t,e,n)=>{n.d(e,{xQ:()=>o});var r=n(2115),i=n(845);function o(t=!0){let e=(0,r.useContext)(i.t);if(null===e)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,r.useCallback)(()=>t&&s&&s(l),[l,s,t]);return!n&&s?[!1,u]:[!0]}},2103:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("mouse-pointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]])},2138:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2486:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2596:(t,e,n)=>{n.d(e,{$:()=>r});function r(){for(var t,e,n=0,r="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=function t(e){var n,r,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(n=0;n<o;n++)e[n]&&(r=t(e[n]))&&(i&&(i+=" "),i+=r)}else for(r in e)e[r]&&(i&&(i+=" "),i+=r);return i}(t))&&(r&&(r+=" "),r+=e);return r}},2605:(t,e,n)=>{let r;n.d(e,{P:()=>op});var i=n(2115);let o=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],s=new Set(o),a=t=>180*t/Math.PI,l=t=>c(a(Math.atan2(t[1],t[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},c=t=>((t%=360)<0&&(t+=360),t),d=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),h=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:h,scale:t=>(d(t)+h(t))/2,rotateX:t=>c(a(Math.atan2(t[6],t[5]))),rotateY:t=>c(a(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let n,r;if(!t||"none"===t)return m(e);let i=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=p,r=i;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=u,r=e}if(!r)return m(e);let o=n[e],s=r[1].split(",").map(g);return"function"==typeof o?o(s):s[o]}function g(t){return parseFloat(t.trim())}let y=t=>e=>"string"==typeof e&&e.startsWith(t),v=y("--"),b=y("var(--"),x=t=>!!b(t)&&w.test(t.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function k({top:t,left:e,right:n,bottom:r}){return{x:{min:e,max:n},y:{min:t,max:r}}}let A=(t,e,n)=>t+(e-t)*n;function E(t){return void 0===t||1===t}function T({scale:t,scaleX:e,scaleY:n}){return!E(t)||!E(e)||!E(n)}function P(t){return T(t)||M(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function M(t){var e,n;return(e=t.x)&&"0%"!==e||(n=t.y)&&"0%"!==n}function S(t,e,n,r,i){return void 0!==i&&(t=r+i*(t-r)),r+n*(t-r)+e}function C(t,e=0,n=1,r,i){t.min=S(t.min,e,n,r,i),t.max=S(t.max,e,n,r,i)}function D(t,{x:e,y:n}){C(t.x,e.translate,e.scale,e.originPoint),C(t.y,n.translate,n.scale,n.originPoint)}function R(t,e){t.min=t.min+e,t.max=t.max+e}function j(t,e,n,r,i=.5){let o=A(t.min,t.max,i);C(t,e,n,o,r)}function V(t,e){j(t.x,e.x,e.scaleX,e.scale,e.originX),j(t.y,e.y,e.scaleY,e.scale,e.originY)}function L(t,e){return k(function(t,e){if(!e)return t;let n=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let O=new Set(["width","height","top","left","right","bottom",...o]),F=(t,e,n)=>n>e?e:n<t?t:n,N={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},I={...N,transform:t=>F(0,1,t)},B={...N,default:1},z=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),U=z("deg"),W=z("%"),$=z("px"),_=z("vh"),H=z("vw"),q={...W,parse:t=>W.parse(t)/100,transform:t=>W.transform(100*t)},Y=t=>e=>e.test(t),X=[N,$,W,U,H,_,{test:t=>"auto"===t,parse:t=>t}],K=t=>X.find(Y(t)),G=()=>{},Z=()=>{},Q=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),J=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tt=t=>t===N||t===$,te=new Set(["x","y","z"]),tn=o.filter(t=>!te.has(t)),tr={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};tr.translateX=tr.x,tr.translateY=tr.y;let ti=t=>t,to={},ts=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ta={value:null,addProjectionMetrics:null};function tl(t,e){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=ts.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,r=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){s.has(e)&&(c.schedule(e),t()),l++,e(a)}let c={schedule:(t,e=!1,o=!1)=>{let a=o&&i?n:r;return e&&s.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),s.delete(t)},process:t=>{if(a=t,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),e&&ta.value&&ta.value.frameloop[e].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(t))}};return c}(o,e?n:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:h,render:p,postRender:m}=s,f=()=>{let o=to.useManualTiming?i.timestamp:performance.now();n=!1,to.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),d.process(i),h.process(i),p.process(i),m.process(i),i.isProcessing=!1,n&&e&&(r=!1,t(f))};return{schedule:ts.reduce((e,o)=>{let a=s[o];return e[o]=(e,o=!1,s=!1)=>(!n&&(n=!0,r=!0,i.isProcessing||t(f)),a.schedule(e,o,s)),e},{}),cancel:t=>{for(let e=0;e<ts.length;e++)s[ts[e]].cancel(t)},state:i,steps:s}}let{schedule:tu,cancel:tc,state:td,steps:th}=tl("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ti,!0),tp=new Set,tm=!1,tf=!1,tg=!1;function ty(){if(tf){let t=Array.from(tp).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{let e=function(t){let e=[];return tn.forEach(n=>{let r=t.getValue(n);void 0!==r&&(e.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tf=!1,tm=!1,tp.forEach(t=>t.complete(tg)),tp.clear()}function tv(){tp.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tf=!0)})}class tb{constructor(t,e,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tp.add(this),tm||(tm=!0,tu.read(tv),tu.resolveKeyframes(ty))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:n,motionValue:r}=this;if(null===t[0]){let i=r?.get(),o=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){let r=n.readValue(e,o);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=o),r&&void 0===i&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tp.delete(this)}cancel(){"scheduled"===this.state&&(tp.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tx=t=>/^0[^.\s]+$/u.test(t),tw=t=>Math.round(1e5*t)/1e5,tk=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tA=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tE=(t,e)=>n=>!!("string"==typeof n&&tA.test(n)&&n.startsWith(t)||e&&null!=n&&Object.prototype.hasOwnProperty.call(n,e)),tT=(t,e,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(tk);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},tP={...N,transform:t=>Math.round(F(0,255,t))},tM={test:tE("rgb","red"),parse:tT("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:r=1})=>"rgba("+tP.transform(t)+", "+tP.transform(e)+", "+tP.transform(n)+", "+tw(I.transform(r))+")"},tS={test:tE("#"),parse:function(t){let e="",n="",r="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),r=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,r+=r,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:tM.transform},tC={test:tE("hsl","hue"),parse:tT("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:r=1})=>"hsla("+Math.round(t)+", "+W.transform(tw(e))+", "+W.transform(tw(n))+", "+tw(I.transform(r))+")"},tD={test:t=>tM.test(t)||tS.test(t)||tC.test(t),parse:t=>tM.test(t)?tM.parse(t):tC.test(t)?tC.parse(t):tS.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tM.transform(t):tC.transform(t),getAnimatableNone:t=>{let e=tD.parse(t);return e.alpha=0,tD.transform(e)}},tR=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tj="number",tV="color",tL=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tO(t){let e=t.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=e.replace(tL,t=>(tD.test(t)?(r.color.push(o),i.push(tV),n.push(tD.parse(t))):t.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(t)):(r.number.push(o),i.push(tj),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function tF(t){return tO(t).values}function tN(t){let{split:e,types:n}=tO(t),r=e.length;return t=>{let i="";for(let o=0;o<r;o++)if(i+=e[o],void 0!==t[o]){let e=n[o];e===tj?i+=tw(t[o]):e===tV?i+=tD.transform(t[o]):i+=t[o]}return i}}let tI=t=>"number"==typeof t?0:tD.test(t)?tD.getAnimatableNone(t):t,tB={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tk)?.length||0)+(t.match(tR)?.length||0)>0},parse:tF,createTransformer:tN,getAnimatableNone:function(t){let e=tF(t);return tN(t)(e.map(tI))}},tz=new Set(["brightness","contrast","saturate","opacity"]);function tU(t){let[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=n.match(tk)||[];if(!r)return t;let i=n.replace(r,""),o=+!!tz.has(e);return r!==n&&(o*=100),e+"("+o+i+")"}let tW=/\b([a-z-]*)\(.*?\)/gu,t$={...tB,getAnimatableNone:t=>{let e=t.match(tW);return e?e.map(tU).join(" "):t}},t_={...N,transform:Math.round},tH={borderWidth:$,borderTopWidth:$,borderRightWidth:$,borderBottomWidth:$,borderLeftWidth:$,borderRadius:$,radius:$,borderTopLeftRadius:$,borderTopRightRadius:$,borderBottomRightRadius:$,borderBottomLeftRadius:$,width:$,maxWidth:$,height:$,maxHeight:$,top:$,right:$,bottom:$,left:$,padding:$,paddingTop:$,paddingRight:$,paddingBottom:$,paddingLeft:$,margin:$,marginTop:$,marginRight:$,marginBottom:$,marginLeft:$,backgroundPositionX:$,backgroundPositionY:$,rotate:U,rotateX:U,rotateY:U,rotateZ:U,scale:B,scaleX:B,scaleY:B,scaleZ:B,skew:U,skewX:U,skewY:U,distance:$,translateX:$,translateY:$,translateZ:$,x:$,y:$,z:$,perspective:$,transformPerspective:$,opacity:I,originX:q,originY:q,originZ:$,zIndex:t_,fillOpacity:I,strokeOpacity:I,numOctaves:t_},tq={...tH,color:tD,backgroundColor:tD,outlineColor:tD,fill:tD,stroke:tD,borderColor:tD,borderTopColor:tD,borderRightColor:tD,borderBottomColor:tD,borderLeftColor:tD,filter:t$,WebkitFilter:t$},tY=t=>tq[t];function tX(t,e){let n=tY(t);return n!==t$&&(n=tB),n.getAnimatableNone?n.getAnimatableNone(e):void 0}let tK=new Set(["auto","none","0"]);class tG extends tb{constructor(t,e,n,r,i){super(t,e,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let r=t[n];if("string"==typeof r&&x(r=r.trim())){let i=function t(e,n,r=1){Z(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[i,o]=function(t){let e=J.exec(t);if(!e)return[,];let[,n,r,i]=e;return[`--${n??r}`,i]}(e);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let t=s.trim();return Q(t)?parseFloat(t):t}return x(o)?t(o,n,r+1):o}(r,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!O.has(n)||2!==t.length)return;let[r,i]=t,o=K(r),s=K(i);if(o!==s)if(tt(o)&&tt(s))for(let e=0;e<t.length;e++){let n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else tr[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||tx(r)))&&n.push(e)}n.length&&function(t,e,n){let r,i=0;for(;i<t.length&&!r;){let e=t[i];"string"==typeof e&&!tK.has(e)&&tO(e).values.length&&(r=t[i]),i++}if(r&&n)for(let i of e)t[i]=tX(n,r)}(t,n,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tr[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(n,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}let tZ=t=>!!(t&&t.getVelocity);function tQ(){r=void 0}let tJ={now:()=>(void 0===r&&tJ.set(td.isProcessing||to.useManualTiming?td.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(tQ)}};function t0(t,e){-1===t.indexOf(e)&&t.push(e)}function t1(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}class t2{constructor(){this.subscriptions=[]}add(t){return t0(this.subscriptions,t),()=>t1(this.subscriptions,t)}notify(t,e,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t5={current:void 0};class t4{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let n=tJ.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tJ.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t2);let n=this.events[t].add(e);return"change"===t?()=>{n(),tu.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t5.current&&t5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tJ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t3(t,e){return new t4(t,e)}let t6=[...X,tD,tB],{schedule:t9}=tl(queueMicrotask,!1),t8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},t7={};for(let t in t8)t7[t]={isEnabled:e=>t8[t].some(t=>!!e[t])};let et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ee=()=>({x:et(),y:et()}),en=()=>({min:0,max:0}),er=()=>({x:en(),y:en()});var ei=n(8972);let eo={current:null},es={current:!1},ea=new WeakMap;function el(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eu(t){return"string"==typeof t||Array.isArray(t)}let ec=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ed=["initial",...ec];function eh(t){return el(t.animate)||ed.some(e=>eu(t[e]))}function ep(t){return!!(eh(t)||t.variants)}function em(t){let e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function ef(t,e,n,r){if("function"==typeof e){let[i,o]=em(r);e=e(void 0!==n?n:t.custom,i,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[i,o]=em(r);e=e(void 0!==n?n:t.custom,i,o)}return e}let eg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ey{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tb,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tJ.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=eh(e),this.isVariantNode=ep(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==a[t]&&tZ(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ea.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),es.current||function(){if(es.current=!0,ei.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>eo.current=t.matches;t.addEventListener("change",e),e()}else eo.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||eo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),tc(this.notifyUpdate),tc(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let n;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=s.has(t);r&&this.onBindTransform&&this.onBindTransform();let i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tu.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),o(),n&&n(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in t7){let e=t7[t];if(!e)continue;let{isEnabled:n,Feature:r}=e;if(!this.features[t]&&r&&n(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):er()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<eg.length;e++){let n=eg[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=t["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(t,e,n){for(let r in e){let i=e[r],o=n[r];if(tZ(i))t.addValue(r,i);else if(tZ(o))t.addValue(r,t3(i,{owner:t}));else if(o!==i)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{let e=t.getStaticValue(r);t.addValue(r,t3(void 0!==e?e:i,{owner:t}))}}for(let r in n)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=t3(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=n){if("string"==typeof n&&(Q(n)||tx(n)))n=parseFloat(n);else{let r;r=n,!t6.find(Y(r))&&tB.test(e)&&(n=tX(t,e))}this.setBaseTarget(t,tZ(n)?n.get():n)}return tZ(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=ef(this.props,n,this.presenceContext?.custom);r&&(e=r[t])}if(n&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||tZ(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new t2),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){t9.render(this.render)}}class ev extends ey{constructor(){super(...arguments),this.KeyframeResolver=tG}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tZ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let eb=(t,e)=>e&&"number"==typeof t?e.transform(t):t,ex={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ew=o.length;function ek(t,e,n){let{style:r,vars:i,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let n=e[t];if(s.has(t)){l=!0;continue}if(v(t)){i[t]=n;continue}{let e=eb(n,tH[t]);t.startsWith("origin")?(u=!0,a[t]=e):r[t]=e}}if(!e.transform&&(l||n?r.transform=function(t,e,n){let r="",i=!0;for(let s=0;s<ew;s++){let a=o[s],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||n){let t=eb(l,tH[a]);if(!u){i=!1;let e=ex[a]||a;r+=`${e}(${t}) `}n&&(e[a]=t)}}return r=r.trim(),n?r=n(e,i?"":r):i&&(r="none"),r}(e,t.transform,n):r.transform&&(r.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:n=0}=a;r.transformOrigin=`${t} ${e} ${n}`}}function eA(t,{style:e,vars:n},r,i){let o,s=t.style;for(o in e)s[o]=e[o];for(o in i?.applyProjectionStyles(s,r),n)s.setProperty(o,n[o])}let eE={};function eT(t,{layout:e,layoutId:n}){return s.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!eE[t]||"opacity"===t)}function eP(t,e,n){let{style:r}=t,i={};for(let o in r)(tZ(r[o])||e.style&&tZ(e.style[o])||eT(o,t)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}class eM extends ev{constructor(){super(...arguments),this.type="html",this.renderInstance=eA}readValueFromInstance(t,e){if(s.has(e))return this.projection?.isProjecting?m(e):((t,e)=>{let{transform:n="none"}=getComputedStyle(t);return f(n,e)})(t,e);{let n=window.getComputedStyle(t),r=(v(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return L(t,e)}build(t,e,n){ek(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return eP(t,e,n)}}let eS=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eC={offset:"stroke-dashoffset",array:"stroke-dasharray"},eD={offset:"strokeDashoffset",array:"strokeDasharray"};function eR(t,{attrX:e,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:s=0,...a},l,u,c){if(ek(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:h}=t;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(t,e,n=1,r=0,i=!0){t.pathLength=1;let o=i?eC:eD;t[o.offset]=$.transform(-r);let s=$.transform(e),a=$.transform(n);t[o.array]=`${s} ${a}`}(d,i,o,s,!1)}let ej=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),eV=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eL(t,e,n){let r=eP(t,e,n);for(let n in t)(tZ(t[n])||tZ(e[n]))&&(r[-1!==o.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return r}class eO extends ev{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=er}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(s.has(e)){let t=tY(e);return t&&t.default||0}return e=ej.has(e)?e:eS(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return eL(t,e,n)}build(t,e,n){eR(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,r){for(let n in eA(t,e,void 0,r),e.attrs)t.setAttribute(ej.has(n)?n:eS(n),e.attrs[n])}mount(t){this.isSVGTag=eV(t.tagName),super.mount(t)}}let eF=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eN(t){if("string"!=typeof t||t.includes("-"));else if(eF.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var eI=n(5155),eB=n(869);let ez=(0,i.createContext)({strict:!1});var eU=n(1508);let eW=(0,i.createContext)({});function e$(t){return Array.isArray(t)?t.join(" "):t}let e_=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eH(t,e,n){for(let r in e)tZ(e[r])||eT(r,n)||(t[r]=e[r])}let eq=()=>({...e_(),attrs:{}}),eY=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eX(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eY.has(t)}let eK=t=>!eX(t);try{!function(t){"function"==typeof t&&(eK=e=>e.startsWith("on")?!eX(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}var eG=n(845),eZ=n(2885);function eQ(t){return tZ(t)?t.get():t}let eJ=t=>(e,n)=>{let r=(0,i.useContext)(eW),o=(0,i.useContext)(eG.t),s=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},n,r,i){return{latestValues:function(t,e,n,r){let i={},o=r(t,{});for(let t in o)i[t]=eQ(o[t]);let{initial:s,animate:a}=t,l=eh(t),u=ep(t);e&&u&&!l&&!1!==t.inherit&&(void 0===s&&(s=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial,d=(c=c||!1===s)?a:s;if(d&&"boolean"!=typeof d&&!el(d)){let e=Array.isArray(d)?d:[d];for(let n=0;n<e.length;n++){let r=ef(t,e[n]);if(r){let{transitionEnd:t,transition:e,...n}=r;for(let t in n){let e=n[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(i[t]=e)}for(let e in t)i[e]=t[e]}}}return i}(n,r,i,t),renderState:e()}})(t,e,r,o);return n?s():(0,eZ.M)(s)},e0=eJ({scrapeMotionValuesFromProps:eP,createRenderState:e_}),e1=eJ({scrapeMotionValuesFromProps:eL,createRenderState:eq}),e2=Symbol.for("motionComponentSymbol");function e5(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e4="data-"+eS("framerAppearId"),e3=(0,i.createContext)({});var e6=n(7494);function e9(t){var e,n;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0;o&&function(t){for(let e in t)t7[e]={...t7[e],...t[e]}}(o);let a=eN(t)?e1:e0;function l(e,n){var o;let l,u={...(0,i.useContext)(eU.Q),...e,layoutId:function(t){let{layoutId:e}=t,n=(0,i.useContext)(eB.L).id;return n&&void 0!==e?n+"-"+e:e}(e)},{isStatic:c}=u,d=function(t){let{initial:e,animate:n}=function(t,e){if(eh(t)){let{initial:e,animate:n}=t;return{initial:!1===e||eu(e)?e:void 0,animate:eu(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,(0,i.useContext)(eW));return(0,i.useMemo)(()=>({initial:e,animate:n}),[e$(e),e$(n)])}(e),h=a(e,c);if(!c&&ei.B){(0,i.useContext)(ez).strict;let e=function(t){let{drag:e,layout:n}=t7;if(!e&&!n)return{};let r={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=e.MeasureLayout,d.visualElement=function(t,e,n,r,o){let{visualElement:s}=(0,i.useContext)(eW),a=(0,i.useContext)(ez),l=(0,i.useContext)(eG.t),u=(0,i.useContext)(eU.Q).reducedMotion,c=(0,i.useRef)(null);r=r||a.renderer,!c.current&&r&&(c.current=r(t,{visualState:e,parent:s,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=c.current,h=(0,i.useContext)(e3);d&&!d.projection&&o&&("html"===d.type||"svg"===d.type)&&function(t,e,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&e5(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,n,o,h);let p=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{d&&p.current&&d.update(n,l)});let m=n[e4],f=(0,i.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return(0,e6.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,i.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),d}(t,h,u,s,e.ProjectionNode)}return(0,eI.jsxs)(eW.Provider,{value:d,children:[l&&d.visualElement?(0,eI.jsx)(l,{visualElement:d.visualElement,...u}):null,function(t,e,n,{latestValues:r},o,s=!1){let a=(eN(t)?function(t,e,n,r){let o=(0,i.useMemo)(()=>{let n=eq();return eR(n,e,eV(r),t.transformTemplate,t.style),{...n.attrs,style:{...n.style}}},[e]);if(t.style){let e={};eH(e,t.style,t),o.style={...e,...o.style}}return o}:function(t,e){let n={},r=function(t,e){let n=t.style||{},r={};return eH(r,n,t),Object.assign(r,function({transformTemplate:t},e){return(0,i.useMemo)(()=>{let n=e_();return ek(n,e,t),Object.assign({},n.vars,n.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=r,n})(e,r,o,t),l=function(t,e,n){let r={};for(let i in t)("values"!==i||"object"!=typeof t.values)&&(eK(i)||!0===n&&eX(i)||!e&&!eX(i)||t.draggable&&i.startsWith("onDrag"))&&(r[i]=t[i]);return r}(e,"string"==typeof t,s),u=t!==i.Fragment?{...l,...a,ref:n}:{},{children:c}=e,d=(0,i.useMemo)(()=>tZ(c)?c.get():c,[c]);return(0,i.createElement)(t,{...u,children:d})}(t,e,(o=d.visualElement,(0,i.useCallback)(t=>{t&&h.onMount&&h.onMount(t),o&&(t?o.mount(t):o.unmount()),n&&("function"==typeof n?n(t):e5(n)&&(n.current=t))},[o])),h,c,r)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(n=null!=(e=t.displayName)?e:t.name)?n:"",")"));let u=(0,i.forwardRef)(l);return u[e2]=t,u}function e8(t,e,n){let r=t.getProps();return ef(r,e,void 0!==n?n:r.custom,t)}function e7(t,e){return t?.[e]??t?.default??t}let nt=t=>Array.isArray(t);function ne(t,e){let n=t.getValue("willChange");if(tZ(n)&&n.add)return n.add(e);if(!n&&to.WillChange){let n=new to.WillChange("auto");t.addValue("willChange",n),n.add(e)}}let nn=(t,e)=>n=>e(t(n)),nr=(...t)=>t.reduce(nn),ni=t=>1e3*t,no={layout:0,mainThread:0,waapi:0};function ns(t,e,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?t+(e-t)*6*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function na(t,e){return n=>n>0?e:t}let nl=(t,e,n)=>{let r=t*t,i=n*(e*e-r)+r;return i<0?0:Math.sqrt(i)},nu=[tS,tM,tC];function nc(t){let e=nu.find(e=>e.test(t));if(G(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let n=e.parse(t);return e===tC&&(n=function({hue:t,saturation:e,lightness:n,alpha:r}){t/=360,n/=100;let i=0,o=0,s=0;if(e/=100){let r=n<.5?n*(1+e):n+e-n*e,a=2*n-r;i=ns(a,r,t+1/3),o=ns(a,r,t),s=ns(a,r,t-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let nd=(t,e)=>{let n=nc(t),r=nc(e);if(!n||!r)return na(t,e);let i={...n};return t=>(i.red=nl(n.red,r.red,t),i.green=nl(n.green,r.green,t),i.blue=nl(n.blue,r.blue,t),i.alpha=A(n.alpha,r.alpha,t),tM.transform(i))},nh=new Set(["none","hidden"]);function np(t,e){return n=>A(t,e,n)}function nm(t){return"number"==typeof t?np:"string"==typeof t?x(t)?na:tD.test(t)?nd:ny:Array.isArray(t)?nf:"object"==typeof t?tD.test(t)?nd:ng:na}function nf(t,e){let n=[...t],r=n.length,i=t.map((t,n)=>nm(t)(t,e[n]));return t=>{for(let e=0;e<r;e++)n[e]=i[e](t);return n}}function ng(t,e){let n={...t,...e},r={};for(let i in n)void 0!==t[i]&&void 0!==e[i]&&(r[i]=nm(t[i])(t[i],e[i]));return t=>{for(let e in r)n[e]=r[e](t);return n}}let ny=(t,e)=>{let n=tB.createTransformer(e),r=tO(t),i=tO(e);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?nh.has(t)&&!i.values.length||nh.has(e)&&!r.values.length?function(t,e){return nh.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):nr(nf(function(t,e){let n=[],r={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){let o=e.types[i],s=t.indexes[o][r[o]],a=t.values[s]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(G(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),na(t,e))};function nv(t,e,n){return"number"==typeof t&&"number"==typeof e&&"number"==typeof n?A(t,e,n):nm(t)(t,e)}let nb=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>tu.update(e,t),stop:()=>tc(e),now:()=>td.isProcessing?td.timestamp:tJ.now()}},nx=(t,e,n=10)=>{let r="",i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)r+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function nw(t){let e=0,n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}function nk(t,e,n){var r,i;let o=Math.max(e-5,0);return r=n-t(o),(i=e-o)?1e3/i*r:0}let nA={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function nE(t,e){return t*Math.sqrt(1-e*e)}let nT=["duration","bounce"],nP=["stiffness","damping","mass"];function nM(t,e){return e.some(e=>void 0!==t[e])}function nS(t=nA.visualDuration,e=nA.bounce){let n,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:nA.velocity,stiffness:nA.stiffness,damping:nA.damping,mass:nA.mass,isResolvedFromDuration:!1,...t};if(!nM(t,nP)&&nM(t,nT))if(t.visualDuration){let n=2*Math.PI/(1.2*t.visualDuration),r=n*n,i=2*F(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:nA.mass,stiffness:r,damping:i}}else{let n=function({duration:t=nA.duration,bounce:e=nA.bounce,velocity:n=nA.velocity,mass:r=nA.mass}){let i,o;G(t<=ni(nA.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let s=1-e;s=F(nA.minDamping,nA.maxDamping,s),t=F(nA.minDuration,nA.maxDuration,t/1e3),s<1?(i=e=>{let r=e*s,i=r*t;return .001-(r-n)/nE(e,s)*Math.exp(-i)},o=e=>{let r=e*s*t,o=Math.pow(s,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=nE(Math.pow(e,2),s);return(r*n+n-o)*a*(-i(e)+.001>0?-1:1)/l}):(i=e=>-.001+Math.exp(-e*t)*((e-n)*t+1),o=e=>t*t*(n-e)*Math.exp(-e*t));let a=function(t,e,n){let r=n;for(let n=1;n<12;n++)r-=t(r)/e(r);return r}(i,o,5/t);if(t=ni(t),isNaN(a))return{stiffness:nA.stiffness,damping:nA.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*s*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...n,mass:nA.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-((r.velocity||0)/1e3)}),f=p||0,g=c/(2*Math.sqrt(u*d)),y=a-s,v=Math.sqrt(u/d)/1e3,b=5>Math.abs(y);if(i||(i=b?nA.restSpeed.granular:nA.restSpeed.default),o||(o=b?nA.restDelta.granular:nA.restDelta.default),g<1){let t=nE(v,g);n=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)n=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);n=e=>{let n=Math.exp(-g*v*e),r=Math.min(t*e,300);return a-n*((f+g*v*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let x={calculatedDuration:m&&h||null,next:t=>{let e=n(t);if(m)l.done=t>=h;else{let r=0===t?f:0;g<1&&(r=0===t?ni(f):nk(n,t,e));let s=Math.abs(a-e)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(nw(x),2e4),e=nx(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function nC({keyframes:t,velocity:e=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h,p=t[0],m={done:!1,value:p},f=n*e,g=p+f,y=void 0===s?g:s(g);y!==g&&(f=y-p);let v=t=>-f*Math.exp(-t/r),b=t=>y+v(t),x=t=>{let e=v(t),n=b(t);m.done=Math.abs(e)<=u,m.value=m.done?y:n},w=t=>{let e;if(e=m.value,void 0!==a&&e<a||void 0!==l&&e>l){var n;d=t,h=nS({keyframes:[m.value,(n=m.value,void 0===a?l:void 0===l||Math.abs(a-n)<Math.abs(l-n)?a:l)],velocity:nk(b,t,m.value),damping:i,stiffness:o,restDelta:u,restSpeed:c})}};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(h||void 0!==d||(e=!0,x(t),w(t)),void 0!==d&&t>=d)?h.next(t-d):(e||x(t),m)}}}nS.applyToOptions=t=>{let e=function(t,e=100,n){let r=n({...t,keyframes:[0,e]}),i=Math.min(nw(r),2e4);return{type:"keyframes",ease:t=>r.next(i*t).value/e,duration:i/1e3}}(t,100,nS);return t.ease=e.ease,t.duration=ni(e.duration),t.type="keyframes",t};let nD=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function nR(t,e,n,r){return t===e&&n===r?ti:i=>0===i||1===i?i:nD(function(t,e,n,r,i){let o,s,a=0;do(o=nD(s=e+(n-e)/2,r,i)-t)>0?n=s:e=s;while(Math.abs(o)>1e-7&&++a<12);return s}(i,0,1,t,n),e,r)}let nj=nR(.42,0,1,1),nV=nR(0,0,.58,1),nL=nR(.42,0,.58,1),nO=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,nF=t=>e=>1-t(1-e),nN=nR(.33,1.53,.69,.99),nI=nF(nN),nB=nO(nI),nz=t=>(t*=2)<1?.5*nI(t):.5*(2-Math.pow(2,-10*(t-1))),nU=t=>1-Math.sin(Math.acos(t)),nW=nF(nU),n$=nO(nU),n_=t=>Array.isArray(t)&&"number"==typeof t[0],nH={linear:ti,easeIn:nj,easeInOut:nL,easeOut:nV,circIn:nU,circInOut:n$,circOut:nW,backIn:nI,backInOut:nB,backOut:nN,anticipate:nz},nq=t=>{if(n_(t)){Z(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,n,r,i]=t;return nR(e,n,r,i)}return"string"==typeof t?(Z(void 0!==nH[t],`Invalid easing type '${t}'`,"invalid-easing-type"),nH[t]):t},nY=(t,e,n)=>{let r=e-t;return 0===r?1:(n-t)/r};function nX({duration:t=300,keyframes:e,times:n,ease:r="easeInOut"}){var i;let o=Array.isArray(r)&&"number"!=typeof r[0]?r.map(nq):nq(r),s={done:!1,value:e[0]},a=function(t,e,{clamp:n=!0,ease:r,mixer:i}={}){let o=t.length;if(Z(o===e.length,"Both input and output ranges must be the same length","range-length"),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];let s=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,n){let r=[],i=n||to.mix||nv,o=t.length-1;for(let n=0;n<o;n++){let o=i(t[n],t[n+1]);e&&(o=nr(Array.isArray(e)?e[n]||ti:e,o)),r.push(o)}return r}(e,r,i),l=a.length,u=n=>{if(s&&n<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(n<t[r+1]);r++);let i=nY(t[r],t[r+1],n);return a[r](i)};return n?e=>u(F(t[0],t[o-1],e)):u}((i=n&&n.length===e.length?n:function(t){let e=[0];return!function(t,e){let n=t[t.length-1];for(let r=1;r<=e;r++){let i=nY(0,e,r);t.push(A(n,1,i))}}(e,t.length-1),e}(e),i.map(e=>e*t)),e,{ease:Array.isArray(o)?o:e.map(()=>o||nL).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=a(e),s.done=e>=t,s)}}let nK=t=>null!==t;function nG(t,{repeat:e,repeatType:n="loop"},r,i=1){let o=t.filter(nK),s=i<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return s&&void 0!==r?r:o[s]}let nZ={decay:nC,inertia:nC,tween:nX,keyframes:nX,spring:nS};function nQ(t){"string"==typeof t.type&&(t.type=nZ[t.type])}class nJ{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let n0=t=>t/100;class n1 extends nJ{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tJ.now()&&this.tick(tJ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},no.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;nQ(t);let{type:e=nX,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=t,{keyframes:s}=t,a=e||nX;a!==nX&&"number"!=typeof s[0]&&(this.mixKeyframes=nr(n0,nv(s[0],s[1])),s=[0,100]);let l=a({...t,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=nw(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=n;if(c){let t=Math.min(this.currentTime,r)/s,e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,(e=Math.min(e,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/s)):"mirror"===d&&(b=o)),v=F(0,1,n)*s}let x=y?{done:!1,value:u[0]}:b.next(v);i&&(x.value=i(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==nC&&(x.value=nG(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=ni(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tJ.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=nb,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tJ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,no.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function n2(t){let e;return()=>(void 0===e&&(e=t()),e)}let n5=n2(()=>void 0!==window.ScrollTimeline),n4={},n3=function(t,e){let n=n2(t);return()=>n4[e]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),n6=([t,e,n,r])=>`cubic-bezier(${t}, ${e}, ${n}, ${r})`,n9={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:n6([0,.65,.55,1]),circOut:n6([.55,0,1,.45]),backIn:n6([.31,.01,.66,-.59]),backOut:n6([.33,1.53,.69,.99])};function n8(t){return"function"==typeof t&&"applyToOptions"in t}class n7 extends nJ{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=t;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=t,Z("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return n8(t)&&n3()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[e]:n};l&&(c.offset=l);let d=function t(e,n){if(e)return"function"==typeof e?n3()?nx(e,n):"ease-out":n_(e)?n6(e):Array.isArray(e)?e.map(e=>t(e,n)||n9.easeOut):n9[e]}(a,i);Array.isArray(d)&&(c.easing=d),ta.value&&no.waapi++;let h={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(h.pseudoElement=u);let p=t.animate(c,h);return ta.value&&p.finished.finally(()=>{no.waapi--}),p}(e,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let t=nG(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){e.startsWith("--")?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=ni(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&n5())?(this.animation.timeline=t,ti):e(this)}}let rt={anticipate:nz,backInOut:nB,circInOut:n$};class re extends n7{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in rt&&(t.ease=rt[t.ease])}(t),nQ(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let s=new n1({...o,autoplay:!1}),a=ni(this.finishedTime??this.time);e.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let rn=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tB.test(t)||"0"===t)&&!t.startsWith("url(")),rr=new Set(["opacity","clipPath","filter","transform"]),ri=n2(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ro extends nJ{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tJ.now();let d={autoplay:t,delay:e,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tb;this.keyframeResolver=new h(s,(t,e,n)=>this.onKeyframesResolved(t,e,d,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=tJ.now(),!function(t,e,n,r){let i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;let o=t[t.length-1],s=rn(i,e),a=rn(o,e);return G(s===a,`You are trying to animate ${e} from "${i}" to "${o}". "${s?o:i}" is not an animatable value.`,"value-not-animatable"),!!s&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||n8(n))&&r)}(t,i,o,s)&&((to.instantAnimations||!a)&&u?.(nG(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},d=!l&&function(t){let{motionValue:e,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ri()&&n&&rr.has(n)&&("transform"!==n||!l)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}(c)?new re({...c,element:c.motionValue.owner.current}):new n1(c);d.finished.then(()=>this.notifyFinished()).catch(ti),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tg=!0,tv(),ty(),tg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let rs=t=>null!==t,ra={type:"spring",stiffness:500,damping:25,restSpeed:10},rl={type:"keyframes",duration:.8},ru={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(t,e,n,r={},i,o)=>a=>{let l=e7(r,t)||{},u=l.delay||r.delay||0,{elapsed:c=0}=r;c-=ni(u);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-c,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:o?void 0:i};!function({when:t,delay:e,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(l)&&Object.assign(d,((t,{keyframes:e})=>e.length>2?rl:s.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:ra:ru)(t,d)),d.duration&&(d.duration=ni(d.duration)),d.repeatDelay&&(d.repeatDelay=ni(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let h=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(h=!0)),(to.instantAnimations||to.skipAnimations)&&(h=!0,d.duration=0,d.delay=0),d.allowFlatten=!l.type&&!l.ease,h&&!o&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:n="loop"},r){let i=t.filter(rs),o=e&&"loop"!==n&&e%2==1?0:i.length-1;return i[o]}(d.keyframes,l);if(void 0!==t)return void tu.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new n1(d):new ro(d)};function rd(t,e,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:s,...a}=e;r&&(o=r);let l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(let e in a){let r=t.getValue(e,t.latestValues[e]??null),i=a[e];if(void 0===i||u&&function({protectedKeys:t,needsAnimating:e},n){let r=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,r}(u,e))continue;let s={delay:n,...e7(o||{},e)},c=r.get();if(void 0!==c&&!r.isAnimating&&!Array.isArray(i)&&i===c&&!s.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=t.props[e4];if(n){let t=window.MotionHandoffAnimation(n,e,tu);null!==t&&(s.startTime=t,d=!0)}}ne(t,e),r.start(rc(e,r,i,t.shouldReduceMotion&&O.has(e)?{type:!1}:s,t,d));let h=r.animation;h&&l.push(h)}return s&&Promise.all(l).then(()=>{tu.update(()=>{s&&function(t,e){let{transitionEnd:n={},transition:r={},...i}=e8(t,e)||{};for(let e in i={...i,...n}){var o;let n=nt(o=i[e])?o[o.length-1]||0:o;t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,t3(n))}}(t,s)})}),l}function rh(t,e,n={}){let r=e8(t,e,"exit"===n.type?t.presenceContext?.custom:void 0),{transition:i=t.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(rd(t,r,n)):()=>Promise.resolve(),s=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(t,e,n=0,r=0,i=0,o=1,s){let a=[],l=t.variantChildren.size,u=(l-1)*i,c="function"==typeof r,d=c?t=>r(t,l):1===o?(t=0)=>t*i:(t=0)=>u-t*i;return Array.from(t.variantChildren).sort(rp).forEach((t,i)=>{t.notify("AnimationStart",e),a.push(rh(t,e,{...s,delay:n+(c?0:r)+d(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r,o,s,a,n)}:()=>Promise.resolve(),{when:a}=i;if(!a)return Promise.all([o(),s(n.delay)]);{let[t,e]="beforeChildren"===a?[o,s]:[s,o];return t().then(()=>e())}}function rp(t,e){return t.sortNodePosition(e)}function rm(t,e){if(!Array.isArray(e))return!1;let n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(e[r]!==t[r])return!1;return!0}let rf=ed.length,rg=[...ec].reverse(),ry=ec.length;function rv(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:rv(!0),whileInView:rv(),whileHover:rv(),whileTap:rv(),whileDrag:rv(),whileFocus:rv(),exit:rv()}}class rx{constructor(t){this.isMounted=!1,this.node=t}update(){}}class rw extends rx{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:n})=>(function(t,e,n={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>rh(t,e,n)));else if("string"==typeof e)r=rh(t,e,n);else{let i="function"==typeof e?e8(t,e,n.custom):e;r=Promise.all(rd(t,i,n))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,n))),n=rb(),r=!0,i=e=>(n,r)=>{let i=e8(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(i){let{transition:t,transitionEnd:e,...r}=i;n={...n,...r,...e}}return n};function o(o){let{props:s}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let n=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(n.initial=e.props.initial),n}let n={};for(let t=0;t<rf;t++){let r=ed[t],i=e.props[r];(eu(i)||!1===i)&&(n[r]=i)}return n}(t.parent)||{},l=[],u=new Set,c={},d=1/0;for(let e=0;e<ry;e++){var h,p;let m=rg[e],f=n[m],g=void 0!==s[m]?s[m]:a[m],y=eu(g),v=m===o?f.isActive:null;!1===v&&(d=e);let b=g===a[m]&&g!==s[m]&&y;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...c},!f.isActive&&null===v||!g&&!f.prevProp||el(g)||"boolean"==typeof g)continue;let x=(h=f.prevProp,"string"==typeof(p=g)?p!==h:!!Array.isArray(p)&&!rm(p,h)),w=x||m===o&&f.isActive&&!b&&y||e>d&&y,k=!1,A=Array.isArray(g)?g:[g],E=A.reduce(i(m),{});!1===v&&(E={});let{prevResolvedValues:T={}}=f,P={...T,...E},M=e=>{w=!0,u.has(e)&&(k=!0,u.delete(e)),f.needsAnimating[e]=!0;let n=t.getValue(e);n&&(n.liveStyle=!1)};for(let t in P){let e=E[t],n=T[t];if(!c.hasOwnProperty(t))(nt(e)&&nt(n)?rm(e,n):e===n)?void 0!==e&&u.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):u.add(t)}f.prevProp=g,f.prevResolvedValues=E,f.isActive&&(c={...c,...E}),r&&t.blockInitialAnimation&&(w=!1);let S=!(b&&x)||k;w&&S&&l.push(...A.map(t=>({animation:t,options:{type:m}})))}if(u.size){let e={};if("boolean"!=typeof s.initial){let n=e8(t,Array.isArray(s.initial)?s.initial[0]:s.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach(n=>{let r=t.getBaseTarget(n),i=t.getValue(n);i&&(i.liveStyle=!0),e[n]=r??null}),l.push({animation:e})}let m=!!l.length;return r&&(!1===s.initial||s.initial===s.animate)&&!t.manuallyAnimateOnMount&&(m=!1),r=!1,m?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,r){if(n[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),n[e].isActive=r;let i=o(e);for(let t in n)n[t].protectedKeys={};return i},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=rb(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();el(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rk=0;class rA extends rx{constructor(){super(...arguments),this.id=rk++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let rE={x:!1,y:!1};function rT(t,e,n,r={passive:!0}){return t.addEventListener(e,n,r),()=>t.removeEventListener(e,n)}let rP=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function rM(t){return{point:{x:t.pageX,y:t.pageY}}}function rS(t,e,n,r){return rT(t,e,t=>rP(t)&&n(t,rM(t)),r)}function rC(t){return t.max-t.min}function rD(t,e,n,r=.5){t.origin=r,t.originPoint=A(e.min,e.max,t.origin),t.scale=rC(n)/rC(e),t.translate=A(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rR(t,e,n,r){rD(t.x,e.x,n.x,r?r.originX:void 0),rD(t.y,e.y,n.y,r?r.originY:void 0)}function rj(t,e,n){t.min=n.min+e.min,t.max=t.min+rC(e)}function rV(t,e,n){t.min=e.min-n.min,t.max=t.min+rC(e)}function rL(t,e,n){rV(t.x,e.x,n.x),rV(t.y,e.y,n.y)}function rO(t){return[t("x"),t("y")]}let rF=({current:t})=>t?t.ownerDocument.defaultView:null,rN=(t,e)=>Math.abs(t-e);class rI{constructor(t,e,{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=rU(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){return Math.sqrt(rN(t.x,e.x)**2+rN(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!n)return;let{point:r}=t,{timestamp:i}=td;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rB(e,this.transformPagePoint),tu.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rU("pointercancel"===t.type?this.lastMoveEventInfo:rB(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),r&&r(t,o)},!rP(t))return;this.dragSnapToOrigin=i,this.handlers=e,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=r||window;let s=rB(rM(t),this.transformPagePoint),{point:a}=s,{timestamp:l}=td;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,rU(s,this.history)),this.removeListeners=nr(rS(this.contextWindow,"pointermove",this.handlePointerMove),rS(this.contextWindow,"pointerup",this.handlePointerUp),rS(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tc(this.updatePoint)}}function rB(t,e){return e?{point:e(t.point)}:t}function rz(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rU({point:t},e){return{point:t,delta:rz(t,rW(e)),offset:rz(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,r=null,i=rW(t);for(;n>=0&&(r=t[n],!(i.timestamp-r.timestamp>ni(.1)));)n--;if(!r)return{x:0,y:0};let o=(i.timestamp-r.timestamp)/1e3;if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(e,.1)}}function rW(t){return t[t.length-1]}function r$(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function r_(t,e){let n=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,r]=[r,n]),{min:n,max:r}}function rH(t,e,n){return{min:rq(t,e),max:rq(t,n)}}function rq(t,e){return"number"==typeof t?t:t[e]||0}let rY=new WeakMap;class rX{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=er(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:n}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rI(t,{onSessionStart:t=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(rM(t).point)},onStart:(t,e)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(rE[t])return null;else return rE[t]=!0,()=>{rE[t]=!1};return rE.x||rE.y?null:(rE.x=rE.y=!0,()=>{rE.x=rE.y=!1})}(n),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rO(t=>{let e=this.getAxisMotionValue(t).get()||0;if(W.test(e)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[t];r&&(e=rC(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),i&&tu.postRender(()=>i(t,e)),ne(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",e.point,s),this.updateAxis("y",e.point,s),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rO(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:n,contextWindow:rF(this.visualElement)})}stop(t,e){let n=t||this.latestPointerEvent,r=e||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;let{velocity:o}=r;this.startAnimation(o);let{onDragEnd:s}=this.getProps();s&&tu.postRender(()=>s(n,r))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){let{drag:r}=this.getProps();if(!n||!rK(t,r,this.currentDirection))return;let i=this.getAxisMotionValue(t),o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},r){return void 0!==e&&t<e?t=r?A(e,t,r.min):Math.max(t,e):void 0!==n&&t>n&&(t=r?A(n,t,r.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&e5(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(t,{top:e,left:n,bottom:r,right:i}){return{x:r$(t.x,n,i),y:r$(t.y,e,r)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:rH(t,"left","right"),y:rH(t,"top","bottom")}}(e),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rO(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!e5(e))return!1;let r=e.current;Z(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(t,e,n){let r=L(t,n),{scroll:i}=e;return i&&(R(r.x,i.offset.x),R(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(t=i.layout.layoutBox,{x:r_(t.x,o.x),y:r_(t.y,o.y)});if(n){let t=n(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(s));this.hasMutatedConstraints=!!t,t&&(s=k(t))}return s}startAnimation(t){let{drag:e,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(rO(s=>{if(!rK(s,e,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?t[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(t,e){let n=this.getAxisMotionValue(t);return ne(this.visualElement,t),n.start(rc(t,n,0,e,this.visualElement,!1))}stopAnimation(){rO(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rO(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){rO(e=>{let{drag:n}=this.getProps();if(!rK(e,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(e);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[e];i.set(t[e]-A(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!e5(e)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rO(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let n=e.get();r[t]=function(t,e){let n=.5,r=rC(t),i=rC(e);return i>r?n=nY(e.min,e.max-r,t.min):r>i&&(n=nY(t.min,t.max-i,e.min)),F(0,1,n)}({min:n,max:n},this.constraints[t])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),rO(e=>{if(!rK(e,t,null))return;let n=this.getAxisMotionValue(e),{min:i,max:o}=this.constraints[e];n.set(A(i,o,r[e]))})}addListeners(){if(!this.visualElement.current)return;rY.set(this.visualElement,this);let t=rS(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e5(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),tu.read(e);let i=rT(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(rO(e=>{let n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{i(),t(),r(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function rK(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}class rG extends rx{constructor(t){super(t),this.removeGroupControls=ti,this.removeListeners=ti,this.controls=new rX(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ti}unmount(){this.removeGroupControls(),this.removeListeners()}}let rZ=t=>(e,n)=>{t&&tu.postRender(()=>t(e,n))};class rQ extends rx{constructor(){super(...arguments),this.removePointerDownListener=ti}onPointerDown(t){this.session=new rI(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rF(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:rZ(t),onStart:rZ(e),onMove:n,onEnd:(t,e)=>{delete this.session,r&&tu.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=rS(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var rJ=n(2082);let r0={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r1(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let r2={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!$.test(t))return t;else t=parseFloat(t);let n=r1(t,e.target.x),r=r1(t,e.target.y);return`${n}% ${r}%`}},r5=!1;class r4 extends i.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=t;for(let t in r6)eE[t]=r6[t],v(t)&&(eE[t].isCSSVariable=!0);i&&(e.group&&e.group.add(i),n&&n.register&&r&&n.register(i),r5&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),r0.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r5=!0,r||t.layoutDependency!==e||void 0===e||t.isPresent!==i?o.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?o.promote():o.relegate()||tu.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),t9.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function r3(t){let[e,n]=(0,rJ.xQ)(),r=(0,i.useContext)(eB.L);return(0,eI.jsx)(r4,{...t,layoutGroup:r,switchLayoutGroup:(0,i.useContext)(e3),isPresent:e,safeToRemove:n})}let r6={borderRadius:{...r2,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r2,borderTopRightRadius:r2,borderBottomLeftRadius:r2,borderBottomRightRadius:r2,boxShadow:{correct:(t,{treeScale:e,projectionDelta:n})=>{let r=tB.parse(t);if(r.length>5)return t;let i=tB.createTransformer(t),o=+("number"!=typeof r[0]),s=n.x.scale*e.x,a=n.y.scale*e.y;r[0+o]/=s,r[1+o]/=a;let l=A(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var r9=n(6983);function r8(t){return(0,r9.G)(t)&&"ownerSVGElement"in t}let r7=(t,e)=>t.depth-e.depth;class it{constructor(){this.children=[],this.isDirty=!1}add(t){t0(this.children,t),this.isDirty=!0}remove(t){t1(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(r7),this.isDirty=!1,this.children.forEach(t)}}let ie=["TopLeft","TopRight","BottomLeft","BottomRight"],ir=ie.length,ii=t=>"string"==typeof t?parseFloat(t):t,io=t=>"number"==typeof t||$.test(t);function is(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let ia=iu(0,.5,nW),il=iu(.5,.95,ti);function iu(t,e,n){return r=>r<t?0:r>e?1:n(nY(t,e,r))}function ic(t,e){t.min=e.min,t.max=e.max}function id(t,e){ic(t.x,e.x),ic(t.y,e.y)}function ih(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ip(t,e,n,r,i){return t-=e,t=r+1/n*(t-r),void 0!==i&&(t=r+1/i*(t-r)),t}function im(t,e,[n,r,i],o,s){!function(t,e=0,n=1,r=.5,i,o=t,s=t){if(W.test(e)&&(e=parseFloat(e),e=A(s.min,s.max,e/100)-s.min),"number"!=typeof e)return;let a=A(o.min,o.max,r);t===o&&(a-=e),t.min=ip(t.min,e,n,a,i),t.max=ip(t.max,e,n,a,i)}(t,e[n],e[r],e[i],e.scale,o,s)}let ig=["x","scaleX","originX"],iy=["y","scaleY","originY"];function iv(t,e,n,r){im(t.x,e,ig,n?n.x:void 0,r?r.x:void 0),im(t.y,e,iy,n?n.y:void 0,r?r.y:void 0)}function ib(t){return 0===t.translate&&1===t.scale}function ix(t){return ib(t.x)&&ib(t.y)}function iw(t,e){return t.min===e.min&&t.max===e.max}function ik(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function iA(t,e){return ik(t.x,e.x)&&ik(t.y,e.y)}function iE(t){return rC(t.x)/rC(t.y)}function iT(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class iP{constructor(){this.members=[]}add(t){t0(this.members,t),t.scheduleRender()}remove(t){if(t1(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,n=this.members.findIndex(e=>t===e);if(0===n)return!1;for(let t=n;t>=0;t--){let n=this.members[t];if(!1!==n.isPresent){e=n;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iM={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iS=["","X","Y","Z"],iC=0;function iD(t,e,n,r){let{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),r&&(r[t]=0))}function iR({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(t={},n=e?.()){this.id=iC++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ta.value&&(iM.nodes=iM.calculatedTargetDeltas=iM.calculatedProjections=0),this.nodes.forEach(iL),this.nodes.forEach(iU),this.nodes.forEach(iW),this.nodes.forEach(iO),ta.addProjectionMetrics&&ta.addProjectionMetrics(iM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new it)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t2),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=r8(e)&&!(r8(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),t){let n,r=0,i=()=>this.root.updateBlockedByResize=!1;tu.read(()=>{r=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==r&&(r=t,this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){let n=tJ.now(),r=({timestamp:e})=>{let i=e-n;i>=250&&(tc(r),t(i-250))};return tu.setup(r,!0),()=>tc(r)}(i,250),r0.hasAnimatedSinceResize&&(r0.hasAnimatedSinceResize=!1,this.nodes.forEach(iz)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||iX,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=i.getProps(),l=!this.targetLayout||!iA(this.targetLayout,r),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...e7(o,"layout"),onPlay:s,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||iz(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),tc(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(i$),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:n}=e.options;if(!n)return;let r=n.props[e4];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:n}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",tu,!(t||n))}let{parent:i}=e;i&&!i.hasCheckedOptimisedAppear&&t(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iN);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(iI);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(iB),this.nodes.forEach(ij),this.nodes.forEach(iV)):this.nodes.forEach(iI),this.clearAllSnapshots();let t=tJ.now();td.delta=F(0,1e3/60,t-td.timestamp),td.timestamp=t,td.isProcessing=!0,th.update.process(td),th.preRender.process(td),th.render.process(td),td.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,t9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iF),this.sharedNodes.forEach(i_)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rC(this.snapshot.measuredBox.x)||rC(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=er(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!i)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!ix(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||P(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let n=this.measurePageBox(),r=this.removeElementScroll(n);return t&&(r=this.removeTransform(r)),iZ((e=r).x),iZ(e.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return er();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(iJ))){let{scroll:t}=this.root;t&&(R(e.x,t.offset.x),R(e.y,t.offset.y))}return e}removeElementScroll(t){let e=er();if(id(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&id(e,t),R(e.x,i.offset.x),R(e.y,i.offset.y))}return e}applyTransform(t,e=!1){let n=er();id(n,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&V(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),P(r.latestValues)&&V(n,r.latestValues)}return P(this.latestValues)&&V(n,this.latestValues),n}removeTransform(t){let e=er();id(e,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];if(!n.instance||!P(n.latestValues))continue;T(n.latestValues)&&n.updateSnapshot();let r=er();id(r,n.measurePageBox()),iv(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return P(this.latestValues)&&iv(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==td.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=td.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rL(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),id(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=er(),this.targetWithTransforms=er()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,rj(o.x,s.x,a.x),rj(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):id(this.target,this.layout.layoutBox),D(this.target,this.targetDelta)):id(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rL(this.relativeTargetOrigin,this.target,t.target),id(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ta.value&&iM.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||T(this.parent.latestValues)||M(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===td.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;id(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(t,e,n,r=!1){let i,o,s=n.length;if(s){e.x=e.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&V(t,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,D(t,o)),r&&P(i.latestValues)&&V(t,i.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=er());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ih(this.prevProjectionDelta.x,this.projectionDelta.x),ih(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rR(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&iT(this.projectionDelta.x,this.prevProjectionDelta.x)&&iT(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ta.value&&iM.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ee(),this.projectionDelta=ee(),this.projectionDeltaWithTransform=ee()}setAnimationOrigin(t,e=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=ee();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=er(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(iY));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(iH(s.x,t.x,r),iH(s.y,t.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;rL(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,iq(p.x,m.x,f.x,g),iq(p.y,m.y,f.y,g),n&&(u=this.relativeTarget,h=n,iw(u.x,h.x)&&iw(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=er()),id(n,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,n,r,i,o){i?(t.opacity=A(0,n.opacity??1,ia(r)),t.opacityExit=A(e.opacity??1,0,il(r))):o&&(t.opacity=A(e.opacity??1,n.opacity??1,r));for(let i=0;i<ir;i++){let o=`border${ie[i]}Radius`,s=is(e,o),a=is(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||io(s)===io(a)?(t[o]=Math.max(A(ii(s),ii(a),r),0),(W.test(a)||W.test(s))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=A(e.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(tc(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tu.update(()=>{r0.hasAnimatedSinceResize=!0,no.layout++,this.motionValue||(this.motionValue=t3(0)),this.currentAnimation=function(t,e,n){let r=tZ(t)?t:t3(t);return r.start(rc("",r,e,n)),r.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{no.layout--},onComplete:()=>{no.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:n,layout:r,latestValues:i}=t;if(e&&n&&r){if(this!==t&&this.layout&&r&&iQ(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||er();let e=rC(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;let r=rC(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+r}id(e,n),V(e,i),rR(this.projectionDeltaWithTransform,this.layoutCorrected,e,i)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iP),this.sharedNodes.get(t).add(e);let n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;let r={};n.z&&iD("z",t,r,this.animationValues);for(let e=0;e<iS.length;e++)iD(`rotate${iS[e]}`,t,r,this.animationValues),iD(`skew${iS[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let n=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eQ(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eQ(e?.pointerEvents)||""),this.hasProjected&&!P(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1);return}t.visibility="";let i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let o=function(t,e,n){let r="",i=t.x.translate/e.x,o=t.y.translate/e.y,s=n?.z||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),n){let{transformPerspective:t,rotate:e,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(o=n(i,o)),t.transform=o;let{x:s,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,eE){if(void 0===i[e])continue;let{correct:n,applyTo:s,isCSSVariable:a}=eE[e],l="none"===o?i[e]:n(i[e],r);if(s){let e=s.length;for(let n=0;n<e;n++)t[s[n]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=r===this?eQ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(iN),this.root.sharedNodes.clear()}}}function ij(t){t.updateLayout()}function iV(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=t.layout,{animationType:i}=t.options,o=e.source!==t.layout.source;"size"===i?rO(t=>{let r=o?e.measuredBox[t]:e.layoutBox[t],i=rC(r);r.min=n[t].min,r.max=r.min+i}):iQ(i,e.layoutBox,n)&&rO(r=>{let i=o?e.measuredBox[r]:e.layoutBox[r],s=rC(n[r]);i.max=i.min+s,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+s)});let s=ee();rR(s,n,e.layoutBox);let a=ee();o?rR(a,t.applyTransform(r,!0),e.measuredBox):rR(a,n,e.layoutBox);let l=!ix(s),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=er();rL(s,e.layoutBox,i.layoutBox);let a=er();rL(a,n,o.layoutBox),iA(s,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=s,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function iL(t){ta.value&&iM.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iO(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iF(t){t.clearSnapshot()}function iN(t){t.clearMeasurements()}function iI(t){t.isLayoutDirty=!1}function iB(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function iz(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function iU(t){t.resolveTargetDelta()}function iW(t){t.calcProjection()}function i$(t){t.resetSkewAndRotation()}function i_(t){t.removeLeadSnapshot()}function iH(t,e,n){t.translate=A(e.translate,0,n),t.scale=A(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function iq(t,e,n,r){t.min=A(e.min,n.min,r),t.max=A(e.max,n.max,r)}function iY(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let iX={duration:.45,ease:[.4,0,.1,1]},iK=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),iG=iK("applewebkit/")&&!iK("chrome/")?Math.round:ti;function iZ(t){t.min=iG(t.min),t.max=iG(t.max)}function iQ(t,e,n){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iE(e)-iE(n)))}function iJ(t){return t!==t.root&&t.scroll?.wasRoot}let i0=iR({attachResizeListener:(t,e)=>rT(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),i1={current:void 0},i2=iR({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!i1.current){let t=new i0({});t.mount(window),t.setOptions({layoutScroll:!0}),i1.current=t}return i1.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function i5(t,e){let n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,n=(void 0)??e.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}(t),r=new AbortController;return[n,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function i4(t){return!("touch"===t.pointerType||rE.x||rE.y)}function i3(t,e,n){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&tu.postRender(()=>i(e,rM(e)))}class i6 extends rx{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){let[r,i,o]=i5(t,n),s=t=>{if(!i4(t))return;let{target:n}=t,r=e(n,t);if("function"!=typeof r||!n)return;let o=t=>{i4(t)&&(r(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(t=>{t.addEventListener("pointerenter",s,i)}),o}(t,(t,e)=>(i3(this.node,e,"Start"),t=>i3(this.node,t,"End"))))}unmount(){}}class i9 extends rx{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=nr(rT(this.node.current,"focus",()=>this.onFocus()),rT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var i8=n(7351);let i7=(t,e)=>!!e&&(t===e||i7(t,e.parentElement)),ot=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),oe=new WeakSet;function on(t){return e=>{"Enter"===e.key&&t(e)}}function or(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function oi(t){return rP(t)&&!(rE.x||rE.y)}function oo(t,e,n){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&tu.postRender(()=>i(e,rM(e)))}class os extends rx{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){let[r,i,o]=i5(t,n),s=t=>{let r=t.currentTarget;if(!oi(t))return;oe.add(r);let o=e(r,t),s=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),oe.has(r)&&oe.delete(r),oi(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{s(t,r===window||r===document||n.useGlobalTarget||i7(r,t.target))},l=t=>{s(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(t=>{((n.useGlobalTarget?window:t).addEventListener("pointerdown",s,i),(0,i8.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let n=t.currentTarget;if(!n)return;let r=on(()=>{if(oe.has(n))return;or(n,"down");let t=on(()=>{or(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>or(n,"cancel"),e)});n.addEventListener("keydown",r,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),e)})(t,i)),ot.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}(t,(t,e)=>(oo(this.node,e,"Start"),(t,{success:e})=>oo(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let oa=new WeakMap,ol=new WeakMap,ou=t=>{let e=oa.get(t.target);e&&e(t)},oc=t=>{t.forEach(ou)},od={some:0,all:1};class oh extends rx{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:r="some",once:i}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:od[r]};return function(t,e,n){let r=function({root:t,...e}){let n=t||document;ol.has(n)||ol.set(n,{});let r=ol.get(n),i=JSON.stringify(e);return r[i]||(r[i]=new IntersectionObserver(oc,{root:t,...e})),r[i]}(e);return oa.set(t,n),r.observe(t),()=>{oa.delete(t),r.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,i&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=e?n:r;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}let op=function(t,e){if("undefined"==typeof Proxy)return e9;let n=new Map,r=(n,r)=>e9(n,r,t,e);return new Proxy((t,e)=>r(t,e),{get:(i,o)=>"create"===o?r:(n.has(o)||n.set(o,e9(o,void 0,t,e)),n.get(o))})}({animation:{Feature:rw},exit:{Feature:rA},inView:{Feature:oh},tap:{Feature:os},focus:{Feature:i9},hover:{Feature:i6},pan:{Feature:rQ},drag:{Feature:rG,ProjectionNode:i2,MeasureLayout:r3},layout:{ProjectionNode:i2,MeasureLayout:r3}},(t,e)=>eN(t)?new eO(e):new eM(e,{allowProjection:t!==i.Fragment}))},2657:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2885:(t,e,n)=>{n.d(e,{M:()=>i});var r=n(2115);function i(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3127:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},3311:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},3332:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3717:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4416:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4869:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5169:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5196:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5525:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5657:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},5690:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6474:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6983:(t,e,n)=>{n.d(e,{G:()=>r});function r(t){return"object"==typeof t&&null!==t}},7351:(t,e,n)=>{n.d(e,{s:()=>i});var r=n(6983);function i(t){return(0,r.G)(t)&&"offsetHeight"in t}},7434:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7494:(t,e,n)=>{n.d(e,{E:()=>i});var r=n(2115);let i=n(8972).B?r.useLayoutEffect:r.useEffect},7576:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},7924:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8126:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("chrome",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["line",{x1:"21.17",x2:"12",y1:"8",y2:"8",key:"a0cw5f"}],["line",{x1:"3.95",x2:"8.54",y1:"6.06",y2:"14",key:"1kftof"}],["line",{x1:"10.88",x2:"15.46",y1:"21.94",y2:"14",key:"1ymyh8"}]])},8136:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8972:(t,e,n)=>{n.d(e,{B:()=>r});let r="undefined"!=typeof window},9074:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9099:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9376:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9621:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},9688:(t,e,n)=>{n.d(e,{QP:()=>tt});let r=(t,e)=>{if(0===t.length)return e.classGroupId;let n=t[0],i=e.nextPart.get(n),o=i?r(t.slice(1),i):void 0;if(o)return o;if(0===e.validators.length)return;let s=t.join("-");return e.validators.find(({validator:t})=>t(s))?.classGroupId},i=/^\[(.+)\]$/,o=(t,e,n,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:s(e,t)).classGroupId=n;return}if("function"==typeof t)return a(t)?void o(t(r),e,n,r):void e.validators.push({validator:t,classGroupId:n});Object.entries(t).forEach(([t,i])=>{o(i,s(e,t),n,r)})})},s=(t,e)=>{let n=t;return e.split("-").forEach(t=>{n.nextPart.has(t)||n.nextPart.set(t,{nextPart:new Map,validators:[]}),n=n.nextPart.get(t)}),n},a=t=>t.isThemeGetter,l=/\s+/;function u(){let t,e,n=0,r="";for(;n<arguments.length;)(t=arguments[n++])&&(e=c(t))&&(r&&(r+=" "),r+=e);return r}let c=t=>{let e;if("string"==typeof t)return t;let n="";for(let r=0;r<t.length;r++)t[r]&&(e=c(t[r]))&&(n&&(n+=" "),n+=e);return n},d=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},h=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,m=/^\d+\/\d+$/,f=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,y=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,v=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=t=>m.test(t),w=t=>!!t&&!Number.isNaN(Number(t)),k=t=>!!t&&Number.isInteger(Number(t)),A=t=>t.endsWith("%")&&w(t.slice(0,-1)),E=t=>f.test(t),T=()=>!0,P=t=>g.test(t)&&!y.test(t),M=()=>!1,S=t=>v.test(t),C=t=>b.test(t),D=t=>!j(t)&&!I(t),R=t=>H(t,K,M),j=t=>h.test(t),V=t=>H(t,G,P),L=t=>H(t,Z,w),O=t=>H(t,Y,M),F=t=>H(t,X,C),N=t=>H(t,J,S),I=t=>p.test(t),B=t=>q(t,G),z=t=>q(t,Q),U=t=>q(t,Y),W=t=>q(t,K),$=t=>q(t,X),_=t=>q(t,J,!0),H=(t,e,n)=>{let r=h.exec(t);return!!r&&(r[1]?e(r[1]):n(r[2]))},q=(t,e,n=!1)=>{let r=p.exec(t);return!!r&&(r[1]?e(r[1]):n)},Y=t=>"position"===t||"percentage"===t,X=t=>"image"===t||"url"===t,K=t=>"length"===t||"size"===t||"bg-size"===t,G=t=>"length"===t,Z=t=>"number"===t,Q=t=>"family-name"===t,J=t=>"shadow"===t;Symbol.toStringTag;let tt=function(t,...e){let n,s,a,c=function(l){let u;return s=(n={cache:(t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++e>t&&(e=0,r=n,n=new Map)};return{get(t){let e=n.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(i(t,e),e):void 0},set(t,e){n.has(t)?n.set(t,e):i(t,e)}}})((u=e.reduce((t,e)=>e(t),t())).cacheSize),parseClassName:(t=>{let{prefix:e,experimentalParseClassName:n}=t,r=t=>{let e,n,r=[],i=0,o=0,s=0;for(let n=0;n<t.length;n++){let a=t[n];if(0===i&&0===o){if(":"===a){r.push(t.slice(s,n)),s=n+1;continue}if("/"===a){e=n;continue}}"["===a?i++:"]"===a?i--:"("===a?o++:")"===a&&o--}let a=0===r.length?t:t.substring(s),l=(n=a).endsWith("!")?n.substring(0,n.length-1):n.startsWith("!")?n.substring(1):n;return{modifiers:r,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",n=r;r=e=>e.startsWith(t)?n(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(n){let t=r;r=e=>n({className:e,parseClassName:t})}return r})(u),sortModifiers:(t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let n=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(n.push(...r.sort(),t),r=[]):r.push(t)}),n.push(...r.sort()),n}})(u),...(t=>{let e=(t=>{let{theme:e,classGroups:n}=t,r={nextPart:new Map,validators:[]};for(let t in n)o(n[t],r,t,e);return r})(t),{conflictingClassGroups:n,conflictingClassGroupModifiers:s}=t;return{getClassGroupId:t=>{let n=t.split("-");return""===n[0]&&1!==n.length&&n.shift(),r(n,e)||(t=>{if(i.test(t)){let e=i.exec(t)[1],n=e?.substring(0,e.indexOf(":"));if(n)return"arbitrary.."+n}})(t)},getConflictingClassGroupIds:(t,e)=>{let r=n[t]||[];return e&&s[t]?[...r,...s[t]]:r}}})(u)}).cache.get,a=n.cache.set,c=d,d(l)};function d(t){let e=s(t);if(e)return e;let r=((t,e)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:o}=e,s=[],a=t.trim().split(l),u="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:l,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:p}=n(e);if(l){u=e+(u.length>0?" "+u:u);continue}let m=!!p,f=r(m?h.substring(0,p):h);if(!f){if(!m||!(f=r(h))){u=e+(u.length>0?" "+u:u);continue}m=!1}let g=o(c).join(":"),y=d?g+"!":g,v=y+f;if(s.includes(v))continue;s.push(v);let b=i(f,m);for(let t=0;t<b.length;++t){let e=b[t];s.push(y+e)}u=e+(u.length>0?" "+u:u)}return u})(t,n);return a(t,r),r}return function(){return c(u.apply(null,arguments))}}(()=>{let t=d("color"),e=d("font"),n=d("text"),r=d("font-weight"),i=d("tracking"),o=d("leading"),s=d("breakpoint"),a=d("container"),l=d("spacing"),u=d("radius"),c=d("shadow"),h=d("inset-shadow"),p=d("text-shadow"),m=d("drop-shadow"),f=d("blur"),g=d("perspective"),y=d("aspect"),v=d("ease"),b=d("animate"),P=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],S=()=>[...M(),I,j],C=()=>["auto","hidden","clip","visible","scroll"],H=()=>["auto","contain","none"],q=()=>[I,j,l],Y=()=>[x,"full","auto",...q()],X=()=>[k,"none","subgrid",I,j],K=()=>["auto",{span:["full",k,I,j]},k,I,j],G=()=>[k,"auto",I,j],Z=()=>["auto","min","max","fr",I,j],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],J=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...q()],te=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],tn=()=>[t,I,j],tr=()=>[...M(),U,O,{position:[I,j]}],ti=()=>["no-repeat",{repeat:["","x","y","space","round"]}],to=()=>["auto","cover","contain",W,R,{size:[I,j]}],ts=()=>[A,B,V],ta=()=>["","none","full",u,I,j],tl=()=>["",w,B,V],tu=()=>["solid","dashed","dotted","double"],tc=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],td=()=>[w,A,U,O],th=()=>["","none",f,I,j],tp=()=>["none",w,I,j],tm=()=>["none",w,I,j],tf=()=>[w,I,j],tg=()=>[x,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[E],breakpoint:[E],color:[T],container:[E],"drop-shadow":[E],ease:["in","out","in-out"],font:[D],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[E],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[E],shadow:[E],spacing:["px",w],text:[E],"text-shadow":[E],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,j,I,y]}],container:["container"],columns:[{columns:[w,j,I,a]}],"break-after":[{"break-after":P()}],"break-before":[{"break-before":P()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:S()}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:H()}],"overscroll-x":[{"overscroll-x":H()}],"overscroll-y":[{"overscroll-y":H()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Y()}],"inset-x":[{"inset-x":Y()}],"inset-y":[{"inset-y":Y()}],start:[{start:Y()}],end:[{end:Y()}],top:[{top:Y()}],right:[{right:Y()}],bottom:[{bottom:Y()}],left:[{left:Y()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",I,j]}],basis:[{basis:[x,"full","auto",a,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",j]}],grow:[{grow:["",w,I,j]}],shrink:[{shrink:["",w,I,j]}],order:[{order:[k,"first","last","none",I,j]}],"grid-cols":[{"grid-cols":X()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":X()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...J(),"normal"]}],"justify-self":[{"justify-self":["auto",...J()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...J(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...J(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...J(),"baseline"]}],"place-self":[{"place-self":["auto",...J()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",n,B,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,I,L]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",A,j]}],"font-family":[{font:[z,j,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,I,j]}],"line-clamp":[{"line-clamp":[w,"none",I,L]}],leading:[{leading:[o,...q()]}],"list-image":[{"list-image":["none",I,j]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",I,j]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:tn()}],"text-color":[{text:tn()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",I,V]}],"text-decoration-color":[{decoration:tn()}],"underline-offset":[{"underline-offset":[w,"auto",I,j]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",I,j]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",I,j]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tr()}],"bg-repeat":[{bg:ti()}],"bg-size":[{bg:to()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,I,j],radial:["",I,j],conic:[k,I,j]},$,F]}],"bg-color":[{bg:tn()}],"gradient-from-pos":[{from:ts()}],"gradient-via-pos":[{via:ts()}],"gradient-to-pos":[{to:ts()}],"gradient-from":[{from:tn()}],"gradient-via":[{via:tn()}],"gradient-to":[{to:tn()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:tn()}],"border-color-x":[{"border-x":tn()}],"border-color-y":[{"border-y":tn()}],"border-color-s":[{"border-s":tn()}],"border-color-e":[{"border-e":tn()}],"border-color-t":[{"border-t":tn()}],"border-color-r":[{"border-r":tn()}],"border-color-b":[{"border-b":tn()}],"border-color-l":[{"border-l":tn()}],"divide-color":[{divide:tn()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,I,j]}],"outline-w":[{outline:["",w,B,V]}],"outline-color":[{outline:tn()}],shadow:[{shadow:["","none",c,_,N]}],"shadow-color":[{shadow:tn()}],"inset-shadow":[{"inset-shadow":["none",h,_,N]}],"inset-shadow-color":[{"inset-shadow":tn()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:tn()}],"ring-offset-w":[{"ring-offset":[w,V]}],"ring-offset-color":[{"ring-offset":tn()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":tn()}],"text-shadow":[{"text-shadow":["none",p,_,N]}],"text-shadow-color":[{"text-shadow":tn()}],opacity:[{opacity:[w,I,j]}],"mix-blend":[{"mix-blend":[...tc(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":tc()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":td()}],"mask-image-linear-to-pos":[{"mask-linear-to":td()}],"mask-image-linear-from-color":[{"mask-linear-from":tn()}],"mask-image-linear-to-color":[{"mask-linear-to":tn()}],"mask-image-t-from-pos":[{"mask-t-from":td()}],"mask-image-t-to-pos":[{"mask-t-to":td()}],"mask-image-t-from-color":[{"mask-t-from":tn()}],"mask-image-t-to-color":[{"mask-t-to":tn()}],"mask-image-r-from-pos":[{"mask-r-from":td()}],"mask-image-r-to-pos":[{"mask-r-to":td()}],"mask-image-r-from-color":[{"mask-r-from":tn()}],"mask-image-r-to-color":[{"mask-r-to":tn()}],"mask-image-b-from-pos":[{"mask-b-from":td()}],"mask-image-b-to-pos":[{"mask-b-to":td()}],"mask-image-b-from-color":[{"mask-b-from":tn()}],"mask-image-b-to-color":[{"mask-b-to":tn()}],"mask-image-l-from-pos":[{"mask-l-from":td()}],"mask-image-l-to-pos":[{"mask-l-to":td()}],"mask-image-l-from-color":[{"mask-l-from":tn()}],"mask-image-l-to-color":[{"mask-l-to":tn()}],"mask-image-x-from-pos":[{"mask-x-from":td()}],"mask-image-x-to-pos":[{"mask-x-to":td()}],"mask-image-x-from-color":[{"mask-x-from":tn()}],"mask-image-x-to-color":[{"mask-x-to":tn()}],"mask-image-y-from-pos":[{"mask-y-from":td()}],"mask-image-y-to-pos":[{"mask-y-to":td()}],"mask-image-y-from-color":[{"mask-y-from":tn()}],"mask-image-y-to-color":[{"mask-y-to":tn()}],"mask-image-radial":[{"mask-radial":[I,j]}],"mask-image-radial-from-pos":[{"mask-radial-from":td()}],"mask-image-radial-to-pos":[{"mask-radial-to":td()}],"mask-image-radial-from-color":[{"mask-radial-from":tn()}],"mask-image-radial-to-color":[{"mask-radial-to":tn()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":M()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":td()}],"mask-image-conic-to-pos":[{"mask-conic-to":td()}],"mask-image-conic-from-color":[{"mask-conic-from":tn()}],"mask-image-conic-to-color":[{"mask-conic-to":tn()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tr()}],"mask-repeat":[{mask:ti()}],"mask-size":[{mask:to()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",I,j]}],filter:[{filter:["","none",I,j]}],blur:[{blur:th()}],brightness:[{brightness:[w,I,j]}],contrast:[{contrast:[w,I,j]}],"drop-shadow":[{"drop-shadow":["","none",m,_,N]}],"drop-shadow-color":[{"drop-shadow":tn()}],grayscale:[{grayscale:["",w,I,j]}],"hue-rotate":[{"hue-rotate":[w,I,j]}],invert:[{invert:["",w,I,j]}],saturate:[{saturate:[w,I,j]}],sepia:[{sepia:["",w,I,j]}],"backdrop-filter":[{"backdrop-filter":["","none",I,j]}],"backdrop-blur":[{"backdrop-blur":th()}],"backdrop-brightness":[{"backdrop-brightness":[w,I,j]}],"backdrop-contrast":[{"backdrop-contrast":[w,I,j]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,I,j]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,I,j]}],"backdrop-invert":[{"backdrop-invert":["",w,I,j]}],"backdrop-opacity":[{"backdrop-opacity":[w,I,j]}],"backdrop-saturate":[{"backdrop-saturate":[w,I,j]}],"backdrop-sepia":[{"backdrop-sepia":["",w,I,j]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",I,j]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",I,j]}],ease:[{ease:["linear","initial",v,I,j]}],delay:[{delay:[w,I,j]}],animate:[{animate:["none",b,I,j]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,I,j]}],"perspective-origin":[{"perspective-origin":S()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[I,j,"","none","gpu","cpu"]}],"transform-origin":[{origin:S()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:tn()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:tn()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",I,j]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",I,j]}],fill:[{fill:["none",...tn()]}],"stroke-w":[{stroke:[w,B,V,L]}],stroke:[{stroke:["none",...tn()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9803:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},9869:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(t,e,n)=>{n.d(e,{A:()=>l});var r=n(2115);let i=t=>{let e=t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,n)=>n?n.toUpperCase():e.toLowerCase());return e.charAt(0).toUpperCase()+e.slice(1)},o=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.filter((t,e,n)=>!!t&&""!==t.trim()&&n.indexOf(t)===e).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((t,e)=>{let{color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:c,iconNode:d,...h}=t;return(0,r.createElement)("svg",{ref:e,...s,width:i,height:i,stroke:n,strokeWidth:l?24*Number(a)/Number(i):a,className:o("lucide",u),...!c&&!(t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0})(h)&&{"aria-hidden":"true"},...h},[...d.map(t=>{let[e,n]=t;return(0,r.createElement)(e,n)}),...Array.isArray(c)?c:[c]])}),l=(t,e)=>{let n=(0,r.forwardRef)((n,s)=>{let{className:l,...u}=n;return(0,r.createElement)(a,{ref:s,iconNode:e,className:o("lucide-".concat(i(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(t),l),...u})});return n.displayName=i(t),n}},9964:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])}}]);